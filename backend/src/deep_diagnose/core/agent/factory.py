"""Graph工厂模块

根据类型创建和返回相应的Graph实例。
"""

from typing import Dict, Any, Optional
from .base import BaseGraph
from deep_diagnose.core.reasoning.reasoning_agent import ReasoningAgent


class GraphFactory:
    """
    根据类型创建和返回相应的 Graph 实例。
    """
    
    @staticmethod
    def create_graph(graph_type: str = "ReasoningAgent", config: Optional[Dict[str, Any]] = None) -> BaseGraph:
        """
        创建 Graph 实例。

        Args:
            graph_type: Graph 的类型标识符
            config: 传递给 Graph 实例的配置

        Returns:
            BaseGraph: 一个具体的 Graph 实例
            
        Raises:
            ValueError: 如果 graph_type 无效
        """
        if graph_type == "ReasoningAgent":
            return ReasoningAgent(config=config)
        # 未来可以扩展其他类型的 Graph
        # elif graph_type == "AnotherAgent":
        #     return AnotherAgent(config=config)
        else:
            raise ValueError(f"未知的 Graph 类型: {graph_type}")