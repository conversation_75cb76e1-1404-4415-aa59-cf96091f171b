"""
Agent基类定义 - 简化的统一基类
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, List, Optional
from langchain_core.runnables import RunnableConfig
from langgraph.types import Command

from deep_diagnose.core.reasoning.workflow.types import State


class BaseAgent(ABC):
    """统一的Agent基类"""
    
    def __init__(self, name: str, agent_type: str, role: str = None, llm_type: str = "reasoning", system_prompt: str = None):
        self.name = name
        self.agent_type = agent_type
        self.role = role or agent_type  # Agent角色描述
        self.llm_type = llm_type        # LLM类型 (basic/reasoning/vision)
        self.system_prompt = system_prompt  # 系统提示词
        self.logger = logging.getLogger(f"{__name__}.{name}")
    
    async def execute(self, state: State, config: Optional[RunnableConfig] = None) -> Command:
        """统一的执行模板"""
        try:
            self.logger.info(f"{self.name} starting")
            result = await self._do_execute(state, config)
            self.logger.info(f"{self.name} completed")
            return result
        except Exception as e:
            self.logger.error(f"{self.name} error: {e}")
            return self._handle_error(e, state)
    
    @abstractmethod
    async def _do_execute(self, state: State, config: Optional[RunnableConfig] = None) -> Command:
        """子类实现具体逻辑"""
        pass
    
    def _handle_error(self, error: Exception, state: State) -> Command:
        """错误处理 - 子类可重写"""
        return Command(goto="__end__")
    
    def get_default_tools(self) -> List[Any]:
        """获取默认工具"""
        return []
    
    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        return self.system_prompt or f"You are {self.name}, a {self.role} agent."