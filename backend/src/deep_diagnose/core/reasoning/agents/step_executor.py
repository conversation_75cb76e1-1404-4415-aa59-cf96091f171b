"""
步骤执行服务 - 专门处理计划步骤的执行逻辑
"""

import logging
from typing import Optional
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage
from langgraph.types import Command

from deep_diagnose.core.reasoning.workflow.types import State
from .utils import setup_mcp_tools, create_agent_with_tools, get_recursion_limit

logger = logging.getLogger(__name__)


class StepExecutor:
    """步骤执行器 - 负责执行计划中的具体步骤"""
    
    def __init__(self, agent_instance):
        self.agent = agent_instance
        self.logger = agent_instance.logger
    
    async def execute_current_step(self, state: State, config: RunnableConfig) -> Command:
        """执行当前步骤"""
        
        try:
            # 1. 找到当前步骤
            current_step = self._find_current_step(state)
            if not current_step:
                self.logger.info("No unexecuted step found")
                return Command(goto="research_team")
            
            self.logger.info(f"Executing step: {current_step.title}")
            
            # 2. 准备工具和创建执行器
            tools = await self._prepare_tools(config)
            executor = await create_agent_with_tools(self.agent.agent_type, tools, config)
            
            # 3. 准备输入并执行
            agent_input = self._prepare_input(state, current_step)
            result = await executor.ainvoke(
                input=agent_input, 
                config={"recursion_limit": get_recursion_limit()}
            )
            
            # 4. 更新结果
            if not result or "messages" not in result or not result["messages"]:
                raise ValueError("Agent execution returned empty or invalid result")
            
            response_content = result["messages"][-1].content
            current_step.execution_res = response_content
            
            self.logger.info(f"Step '{current_step.title}' completed")
            
            return Command(
                update={
                    "messages": [HumanMessage(content=response_content, name=self.agent.agent_type)],
                    "observations": state.get("observations", []) + [response_content],
                },
                goto="research_team",
            )
            
        except Exception as e:
            self.logger.error(f"Error executing step: {e}")
            import traceback
            self.logger.error(f"Full traceback:\n{traceback.format_exc()}")
            
            # 返回错误信息而不是崩溃
            error_message = f"Step execution failed: {str(e)}"
            if 'current_step' in locals():
                current_step.execution_res = error_message
            
            return Command(
                update={
                    "messages": [HumanMessage(content=error_message, name=self.agent.agent_type)],
                    "observations": state.get("observations", []) + [error_message],
                },
                goto="research_team",
            )
    
    def _find_current_step(self, state: State):
        """找到当前需要执行的步骤"""
        current_plan = state.get("current_plan")
        if not current_plan or not hasattr(current_plan, 'steps') or not current_plan.steps:
            return None
        
        for step in current_plan.steps:
            if not step.execution_res:
                return step
        return None
    
    async def _prepare_tools(self, config: RunnableConfig):
        """准备工具"""
        try:
            default_tools = self.agent.get_default_tools()
            mcp_tools = await setup_mcp_tools(self.agent.agent_type, config)
            return default_tools + mcp_tools
            
        except Exception as e:
            self.logger.error(f"Error preparing tools: {e}")
            import traceback
            self.logger.error(f"Tools preparation traceback:\n{traceback.format_exc()}")
            
            # 如果 MCP 工具失败，至少返回默认工具
            try:
                default_tools = self.agent.get_default_tools()
                self.logger.warning(f"Falling back to {len(default_tools)} default tools only")
                return default_tools
            except Exception as fallback_error:
                self.logger.error(f"Even default tools failed: {fallback_error}")
                import traceback
                self.logger.error(f"Fallback traceback:\n{traceback.format_exc()}")
                return []
    
    def _prepare_input(self, state: State, current_step):
        """准备执行输入"""
        context_builder = StepContextBuilder(state, current_step)
        return context_builder.build_input(self.agent.agent_type)


class StepContextBuilder:
    """步骤上下文构建器 - 负责构建清晰的执行上下文"""
    
    def __init__(self, state: State, current_step):
        self.state = state
        self.current_step = current_step
        self.current_plan = state.get("current_plan")
    
    def build_input(self, agent_type: str) -> dict:
        """构建agent输入"""
        messages = []
        
        # 添加历史步骤上下文
        history_context = self._build_history_context()
        if history_context:
            messages.append(HumanMessage(content=history_context))
        
        # 添加当前任务
        current_task = self._build_current_task()
        messages.append(HumanMessage(content=current_task))
        
        # 添加特定agent的指令
        agent_instruction = self._build_agent_instruction(agent_type)
        if agent_instruction:
            messages.append(HumanMessage(content=agent_instruction, name="system"))
        
        return {"messages": messages}
    
    def _build_history_context(self) -> str:
        """构建历史步骤上下文"""
        completed_steps = [
            step for step in self.current_plan.steps 
            if step.execution_res and step != self.current_step
        ]
        
        if not completed_steps:
            return ""
        
        context_parts = ["# 前置诊断步骤\n"]
        
        for i, step in enumerate(completed_steps, 1):
            context_parts.extend([
                f"## 步骤 {i}: {step.title}\n",
                f"**执行结果:**\n",
                f"```\n{step.execution_res}\n```\n"
            ])
        
        return "\n".join(context_parts)
    
    def _build_current_task(self) -> str:
        """构建当前任务描述"""
        return f"""# 当前诊断任务

## 任务标题
{self.current_step.title}

## 任务描述  
{self.current_step.description}

## 执行要求
请根据任务描述执行相应的诊断操作，并提供详细的分析结果。"""
    
    def _build_agent_instruction(self, agent_type: str) -> Optional[str]:
        """构建特定agent的指令"""
        instructions = {
            "researcher": """**重要提示:**
- 当工具执行返回的data字段为空时，表示"未查询到相关数据"
- 严禁编造或虚构任何信息
- 确保回应完全基于工具的实际输出
- 请用中文回答""",
            
            "coder": """**代码分析要求:**
- 仔细分析代码逻辑和潜在问题
- 提供具体的修复建议
- 如需执行代码，请确保安全性"""
        }
        
        return instructions.get(agent_type)