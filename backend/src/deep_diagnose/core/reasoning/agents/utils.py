"""
Agent工具函数 - 纯粹的公共工具，不包含业务逻辑
"""

import os
import logging
from typing import List
from langchain_core.runnables import RunnableConfig
from langchain_mcp_adapters.client import MultiServerMCPClient

from deep_diagnose.common.config.core.configuration import Configuration
from deep_diagnose.common.config.constants.workflow import DEFAULT_MAX_RECURSIVE_NUM
from deep_diagnose.common.config.constants.agents import AGENT_LLM_MAP
from deep_diagnose.llms.llm import get_llm_by_type
from deep_diagnose.prompts.template import apply_prompt_template
from deep_diagnose.tools.mcp.mcp_tool_manager import MCPToolManager

logger = logging.getLogger(__name__)


async def setup_mcp_tools(agent_type: str, config: RunnableConfig) -> List:
    """为指定agent设置MCP工具 - 纯工具函数"""
    try:
        mcp_tool_manager = MCPToolManager()
        # 使用新的MCP服务获取过滤后的工具
        filtered_tools = await mcp_tool_manager.get_enabled_mcp_tools()
        logger.info(f"Loaded {len(filtered_tools)} MCP tools for {agent_type}")
        return filtered_tools
    except Exception as e:
        logger.error(f"Failed to setup MCP tools for {agent_type}: {e}")
        return []


async def create_agent_with_tools(agent_type: str, tools: List, config: RunnableConfig):
    """创建带工具的agent - 支持并发工具调用"""
    from langgraph.prebuilt import create_react_agent
    
    configurable = Configuration.from_runnable_config(config)
    mcp_tool_manager = MCPToolManager()

    async def generate_prompt(agent_state):
        agent_state["mcp_servers_description"] = await mcp_tool_manager.get_enabled_mcp_tools_description()
        return apply_prompt_template(agent_type, agent_state)
    
    # 获取 LLM 并启用并发工具调用
    llm = get_llm_by_type(AGENT_LLM_MAP[agent_type])
    
    # 确保 LLM 支持并发工具调用
    if hasattr(llm, 'parallel_tool_calls'):
        llm.parallel_tool_calls = True
    elif hasattr(llm, 'bind'):
        # 使用 bind 方法启用并发工具调用
        llm = llm.bind(parallel_tool_calls=True)
    
    return create_react_agent(
        name=agent_type,
        model=llm,
        tools=tools,
        prompt=generate_prompt,
    )


def get_recursion_limit() -> int:
    """获取递归限制配置 - 纯工具函数"""
    default_limit = DEFAULT_MAX_RECURSIVE_NUM
    return default_limit
    
