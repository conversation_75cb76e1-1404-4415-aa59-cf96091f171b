"""ReasoningAgent实现

基于LangGraph的诊断Agent实现，包含原ChatService中的核心逻辑。
"""

import json
import logging
import traceback
from typing import AsyncIterator, Dict, Any, List, Optional, cast
from uuid import uuid4

from langchain_core.messages import AIMessageChunk, ToolMessage, BaseMessage
from langgraph.types import Command

from deep_diagnose.common.config.constants.workflow import (
    DEFAULT_MAX_STEP_NUM, 
    DEFAULT_AUTO_ACCEPTED_PLAN, 
    DEFAULT_ENABLE_BACKGROUND_INVESTIGATION
)
from deep_diagnose.core.reasoning.agents.utils import get_recursion_limit
from deep_diagnose.tools.mcp.mcp_tool_config import MCPToolConfig
from deep_diagnose.core.agent.base import BaseGraph
from deep_diagnose.core.reasoning.workflow.builder import build_graph_with_memory

logger = logging.getLogger(__name__)


class ReasoningAgent(BaseGraph):
    """
    基于 LangGraph 的诊断 Agent 实现。
    包含原ChatService中的所有Graph相关逻辑。
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化ReasoningAgent
        
        Args:
            config: 配置字典
        """
        super().__init__(config)
        
        # 内部构建 LangChain Graph workflow
        self._graph = build_graph_with_memory()
        self._langfuse_handler = None
        
        # 从配置中获取可选的依赖项
        if config:
            # 允许外部覆盖langfuse_handler
            self._langfuse_handler = config.get("langfuse_handler")
    
    
    def make_event(self, event_type: str, data: Dict[str, Any]) -> str:
        """创建SSE事件
        
        Args:
            event_type: 事件类型
            data: 事件数据
            
        Returns:
            格式化的SSE事件字符串
        """
        if data.get("content") == "":
            data.pop("content")
        return f"event: {event_type}\ndata: {json.dumps(data, ensure_ascii=False)}\n\n"
    
    async def astream(
        self, 
        question: str, 
        messages: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> AsyncIterator[str]:
        """
        执行 LangChain Graph 并流式返回结果。
        
        Args:
            question: 用户问题
            messages: 聊天消息列表
            **kwargs: 其他参数
            
        Yields:
            str: SSE格式的事件流字符串
        """
        # 记录函数开始时的question和messages信息
        logger.info(f"ReasoningAgent.astream started - question: {question}")
        logger.info(f"ReasoningAgent.astream started - messages: {messages}")
        
        # 如果没有提供messages，则从question构建
        if messages is None or len(messages)==0:
            messages = [{"role": "user", "content": question}]
        
        # 生成thread_id
        thread_id = str(uuid4())
        
        # 使用默认MCP设置
        mcp_settings = MCPToolConfig.get_server_configs()
        
        try:
            # 使用内部默认配置
            auto_accepted_plan = True  # ReasoningAgent内部默认值
            enable_background_investigation = False  # ReasoningAgent内部默认值
            interrupt_feedback = None  # ReasoningAgent内部默认值
            max_plan_iterations = 1  # ReasoningAgent内部默认值
            max_search_results = 3  # ReasoningAgent内部默认值
            
            # 构建工作流输入
            input_data = {
                "messages": messages,
                "plan_iterations": self.plan_iterations,
                "final_report": self.final_report,
                "current_plan": self.current_plan,
                "observations": self.observations,
                "auto_accepted_plan": auto_accepted_plan,
                "enable_background_investigation": enable_background_investigation,
            }
            
            # 处理中断反馈
            if not auto_accepted_plan and interrupt_feedback:
                resume_msg = f"[{interrupt_feedback}]"
                # 将最后一条消息添加到恢复消息中
                if messages:
                    resume_msg += f" {messages[-1]['content']}"
                input_data = Command(resume=resume_msg)
            
            # 构建配置
            config = {
                "thread_id": thread_id,
                "max_step_num": 10,  # ReasoningAgent内部默认值
                "mcp_settings": mcp_settings,
                "recursion_limit": get_recursion_limit()
            }
            
            # 添加langfuse_handler（如果存在）
            if self._langfuse_handler:
                config["callbacks"] = [self._langfuse_handler]
            
            # 添加可选配置项
            if max_plan_iterations is not None:
                config["max_plan_iterations"] = max_plan_iterations
            if max_search_results is not None:
                config["max_search_results"] = max_search_results
            
            # 执行工作流并处理事件流
            async for agent, _, event_data in self._graph.astream(
                input_data,
                config=config,
                stream_mode=["messages", "updates"],
                subgraphs=True,
            ):
                # 处理静默标签过滤
                if isinstance(event_data, tuple) and len(event_data) == 2:
                    message, metadata = event_data
                    
                    # 检查元数据中是否包含静默标签
                    if "silent_llm_call" in metadata.get("tags", []):
                        logger.info(f"Ignoring event from {agent} with tag 'silent_llm_call'.")
                        continue
                
                # 处理中断事件
                if isinstance(event_data, dict):
                    if "__interrupt__" in event_data:
                        yield self.make_event(
                            "interrupt",
                            {
                                "thread_id": thread_id,
                                "id": event_data["__interrupt__"][0].ns[0],
                                "role": "assistant",
                                "content": event_data["__interrupt__"][0].value,
                                "finish_reason": "interrupt",
                                "options": [
                                    {"text": "Edit plan", "value": "edit_plan"},
                                    {"text": "Start research", "value": "accepted"},
                                ],
                            },
                        )
                    continue
                
                # 处理消息事件
                message_chunk, message_metadata = cast(
                    tuple[BaseMessage, dict[str, Any]], event_data
                )
                
                event_stream_message: Dict[str, Any] = {
                    "thread_id": thread_id,
                    "agent": agent[0].split(":")[0],
                    "id": message_chunk.id,
                    "role": "assistant",
                    "content": message_chunk.content,
                }
                
                # 添加完成原因（如果存在）
                if message_chunk.response_metadata.get("finish_reason"):
                    event_stream_message["finish_reason"] = message_chunk.response_metadata.get(
                        "finish_reason"
                    )
                
                # 根据消息类型处理不同的事件
                if isinstance(message_chunk, ToolMessage):
                    # 工具消息 - 返回工具调用结果
                    event_stream_message["tool_call_id"] = message_chunk.tool_call_id
                    yield self.make_event("tool_call_result", event_stream_message)
                elif isinstance(message_chunk, AIMessageChunk):
                    # AI消息 - 原始消息令牌
                    if message_chunk.tool_calls:
                        # AI消息 - 工具调用
                        event_stream_message["tool_calls"] = message_chunk.tool_calls
                        event_stream_message["tool_call_chunks"] = (
                            message_chunk.tool_call_chunks
                        )
                        yield self.make_event("tool_calls", event_stream_message)
                    elif message_chunk.tool_call_chunks:
                        # AI消息 - 工具调用块
                        event_stream_message["tool_call_chunks"] = (
                            message_chunk.tool_call_chunks
                        )
                        yield self.make_event("tool_call_chunks", event_stream_message)
                    else:
                        # AI消息 - 原始消息令牌
                        yield self.make_event("message_chunk", event_stream_message)
        
        except Exception as e:
            # 记录完整的错误信息和堆栈
            logger.error(
                f"An error occurred during chat stream for thread_id {thread_id}: {e}\n"
                f"{traceback.format_exc()}"
            )
            
            # 创建错误事件发送给客户端
            error_data = {
                "thread_id": thread_id,
                "error": type(e).__name__,
                "message": str(e),
                "traceback": traceback.format_exc()
            }
            yield self.make_event("error", error_data)