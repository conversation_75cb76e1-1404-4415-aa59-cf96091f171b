"""
MCP Celery任务

职责：
- 定期刷新MCP工具缓存
- 验证MCP服务器状态
- 支持手动触发刷新
"""

import asyncio
import logging

from deep_diagnose.common.config import get_config
from deep_diagnose.services.task.celery_base import celery
from deep_diagnose.tools.mcp.mcp_tool_cache import MCPCacheManager
logger = logging.getLogger(__name__)


@celery.task(queue=get_config().app_queue, name="refresh_mcp_cache")
def refresh_mcp_cache_task() -> bool:
    """
    刷新MCP工具缓存任务
    
    Returns:
        bool: 是否成功
    """
    try:
        logger.info("Starting MCP cache refresh task")
        
        # 使用 asyncio.run() 创建独立的事件循环，避免与现有循环冲突
        def run_refresh():
            cache_manager = MCPCacheManager()
            return asyncio.run(cache_manager.refresh_tools())
        
        result = run_refresh()
        
        total_tools = sum(len(tools) for tools in result.values())
        logger.info(f"Cache refresh completed: {len(result)} servers, {total_tools} tools")
        
        return True
        
    except Exception as e:
        logger.error(f"MCP cache refresh failed: {e}")
        return False
