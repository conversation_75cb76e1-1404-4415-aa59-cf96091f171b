"""
MCP统一管理器

职责：
- 协调配置、缓存、格式化等组件
- 提供统一的对外接口
- 管理工具生命周期
"""

import logging
import json
from typing import Dict, List

from langchain_core.tools import BaseTool
from langchain_core.utils.function_calling import convert_to_openai_tool

from deep_diagnose.tools.mcp.mcp_tool_cache import MCPCacheManager

logger = logging.getLogger(__name__)


class MCPToolManager:
    """MCP统一管理器"""
    
    def __init__(self):
        """初始化管理器"""
        self.cache_manager = MCPCacheManager()

    async def get_enabled_mcp_tools(self) -> List[BaseTool]:
        """
        获取所有MCP工具
        
        Returns:
            Dict: {server_name: [tools]}
        """
        try:
            tools_dict = await self.cache_manager.get_tools()
            all_tools = []
            for server_tools in tools_dict.values():
                all_tools.extend(server_tools)
            return all_tools
        except Exception as e:
            logger.error(f"Failed to get tools: {e}")
            return []
    
    async def get_enabled_mcp_tools_description(self) -> str:
        """
        获取工具描述文档
        
        Returns:
            str: Markdown格式的工具描述
        """
        try:
            # 获取所有工具
            tools_dict = await self.cache_manager.get_tools()
            
            if tools_dict:
                logger.info(f"Found {len(tools_dict)} servers in tools_dict")
                for server_name, tools in tools_dict.items():
                    logger.info(f"Server '{server_name}': {len(tools)} tools")
                    if tools:
                        tool_names = [tool.name for tool in tools]
                        logger.info(f"  Tool names: {tool_names}")
            
            # 检查是否有有效工具
            total_tools = sum(len(tools) for tools in tools_dict.values()) if tools_dict else 0
            logger.info(f"Total tools count: {total_tools}")
            
            if not tools_dict or total_tools == 0:
                logger.warning(f"No valid MCP tools found. tools_dict: {bool(tools_dict)}, total_tools: {total_tools}")
                return "## Available Tools\nNo MCP tools available."
            
            # 格式化描述
            description = self._format_tools_description(tools_dict)
            
            # 记录最终返回的工具数量（直接统计实际工具数量）
            final_tool_count = sum(len(tools) for tools in tools_dict.values())
            logger.info(f"Final description contains {final_tool_count} tools")
            
            return description
            
        except Exception as e:
            logger.error(f"Failed to get tools description: {e}")
            import traceback
            logger.error(f"Full traceback:\n{traceback.format_exc()}")
            return "## Available Tools\nError generating tools description."

    def _format_tools_description(self, tools_dict: Dict[str, List[BaseTool]]) -> str:
        """
        格式化所有工具为描述文档

        Args:
            tools_dict: {server_name: [tools]}

        Returns:
            str: Markdown格式的工具描述
        """
        if not tools_dict:
            return "## Available Tools\nNo MCP tools available."

        tool_descriptions = []
        for server_name, tools in tools_dict.items():
            for tool in tools:
                try:
                    description = self._format_single_tool(tool)
                    tool_descriptions.append(description)
                except Exception as e:
                    logger.error(f"Failed to format tool '{tool.name}': {e}")

        if not tool_descriptions:
            logger.warning("No tool descriptions generated")
            return "## Available Tools\nNo enabled MCP tools found."

        return "## Available Tools\n" + '\n\n'.join(tool_descriptions)

    def _format_single_tool(self, tool: BaseTool) -> str:
        """
        格式化单个工具描述

        Args:
            tool: 工具对象

        Returns:
            str: 格式化的工具描述
        """
        try:
            # 获取工具schema
            openai_tool = convert_to_openai_tool(tool, strict=True)
            schema = openai_tool['function']['parameters']

            # 格式化schema
            schema_json = json.dumps(schema, indent=2, ensure_ascii=False)
            schema_lines = schema_json.splitlines()
            schema_indented = '\n'.join(f'    {line}' for line in schema_lines)

            # 组合描述
            return f'- {tool.name}: {tool.description}\n    Input Schema:\n{schema_indented}'

        except Exception as e:
            logger.error(f"Failed to format tool {tool.name}: {e}")
            return f'- {tool.name}: {tool.description}\n    Input Schema: Error formatting schema'

