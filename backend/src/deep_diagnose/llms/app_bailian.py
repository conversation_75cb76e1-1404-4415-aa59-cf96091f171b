"""
对百炼应用的调用封装
"""


import logging
from enum import Enum
from http import HTTPStatus
from typing import Callable

from dashscope import Application
from dashscope.app.application_response import ApplicationOutput

from deep_diagnose.common.config import get_config


class BailianAppEnum(Enum):
    """
    枚举类：百炼应用相关ID
    """
    planner = "50cc2154770042148b1e6f2af9586f6e"
    param_extractor = "e74bfaf654d74e18bf75eded4cd5405f"
    concluder = "79484d517d6e4d66aaedc30c3589a676"


class AppBailian:
    def __init__(self, api_key: str):
        self.__api_key__ = api_key

    def stream_call(self, app: BailianAppEnum, prompt: str, biz_params: dict, output_handler: Callable[[ApplicationOutput], None]) -> bool:
        """
        同步调用百炼应用的流式接口，并处理输出结果。
        
        参数:
            app (BailianAppEnum): 指定调用的百炼应用实例，使用枚举类型标识不同的应用。
            prompt (str): 输入给模型的提示文本。
            biz_params (dict): 业务参数，用于传递额外的上下文信息。
            output_handler (Callable[[ApplicationOutput], None]): 输出处理器，用于处理每个流式返回的输出对象。

        返回值:
            bool: 如果百炼响应成功则返回True，否则在发生错误时记录日志并返回False。
        """

        responses = Application.call(
            api_key=self.__api_key__,
            app_id=app.value,
            prompt=prompt,
            biz_params=biz_params,
            # 开启流式输出
            stream=True,
            # 开启兼容模式，输出指定节点的流式结果
            flow_stream_mode="agent_format",
            # incremental_output为true开启增量输出，为false关闭增量输出，不填写默认false
            incremental_output=True)
        for response in responses:
            if response.status_code != HTTPStatus.OK:
                # 输出错误信息
                _logger.error(
                    f'request_id={response.request_id}, code={response.status_code}, message={response.message}')
                return False
            else:
                output_handler(response.output)
        return True


def _load_key_from_env() -> str:
    key = get_config().llm.tongyi_provider.api_key
    return key


# 模块初始化代码
_logger = logging.getLogger(__name__)
app_bailian = AppBailian(_load_key_from_env())

