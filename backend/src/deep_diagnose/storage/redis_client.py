import redis
from redis import Redis

from deep_diagnose.common.config import get_config


class RedisClient:
    def __init__(
            self,
            host=get_config().infrastructure.redis.host,
            port=get_config().infrastructure.redis.port,
            username=get_config().infrastructure.redis.user,
            password=get_config().infrastructure.redis.password
    ):
        self.host = host
        self.port = port
        self.username = username
        self.password = password

    def _get_client(self) -> Redis:
        return redis.StrictRedis(
            host=self.host,
            port=self.port,
            username=self.username,
            password=self.password,
            decode_responses=True
        )

    def get_cache_keys(self, pattern):
        client = self._get_client()
        keys = client.keys(pattern)
        client.close()
        return keys

    def set_cache(self, key, value, ttl_seconds=3600):
        client = self._get_client()
        client.setex(key, ttl_seconds, value)
        client.close()

    def get_cache(self, key):
        client = self._get_client()
        result = client.get(key)
        client.close()
        return result

    def delete_cache(self, key):
        client = self._get_client()
        client.delete(key)
        client.close()