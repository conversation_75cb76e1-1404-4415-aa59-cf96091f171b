"""聊天服务模块

重构后的ChatService，专注于服务层职责：
- 接收外部请求
- 验证用户和会话信息  
- 通过GraphFactory创建Graph实例
- 调用Graph方法返回结果

遵循单一职责原则，不再处理Graph内部逻辑。
"""

import logging
from typing import List, Dict, Any, AsyncGenerator, Optional
from uuid import uuid4

from langfuse.callback import CallbackHandler
from deep_diagnose.core.agent.factory import GraphFactory
from deep_diagnose.common.config import get_config
from deep_diagnose.domain.chat.repository import chat_repository
from deep_diagnose.domain.chat.models import CloudbotAgentChatSession, CloudbotAgentChatMessage
from deep_diagnose.services.sse.sse_message_parser import SSEMessageParser
import json

logger = logging.getLogger(__name__)

# 初始化全局依赖项

class ChatService:
    """聊天服务类
    
    重构后的ChatService专注于服务层职责：
    - 作为API入口和请求分发层
    - 验证用户和会话信息
    - 通过GraphFactory创建Graph实例
    - 调用Graph方法返回结果
    
    特点：无状态，不持有Graph执行相关的中间状态
    """
    
    def __init__(self):
        """初始化聊天服务
        
        Args:
            graph_instance: LangGraph工作流图对象（可选，默认使用全局实例）
            langfuse_instance: Langfuse回调处理器（可选，默认使用全局实例）
        """
        # 使用全局依赖项，但允许外部覆盖
        self.graph_factory = GraphFactory()
    
    
    async def _handle_session_and_context(
        self, 
        user_id: str, 
        session_id: Optional[str], 
        question: str,
        instance_id: str,
        messages: Optional[List[Dict[str, Any]]]
    ) -> tuple[str, Optional[List[Dict[str, Any]]], int]:
        """处理会话管理和上下文加载
        
        Args:
            user_id: 用户ID
            session_id: 会话ID
            question: 用户问题
            instance_id: 实例ID
            messages: 现有消息列表
            
        Returns:
            tuple: (最终的session_id, 消息上下文列表, 用户消息ID)
        """

        if not session_id or len(session_id)==0:
            # 创建新会话
            chat_session = await chat_repository.create_session(
                user_id=user_id,
                title=question or f"Chat with {user_id}"
            )
            session_id = chat_session.session_id
            logger.info(f"Created new session {session_id} for user {user_id}")
        else:
            messages = []
            history_messages = await chat_repository.get_session_messages(session_id)
            for msg in history_messages:
                role = "user" if msg.message_type == "human" else "assistant"
                messages.append({
                    "role": role,
                    "content": msg.message
                })
            logger.info(f"Loaded {len(messages)} history messages for session {session_id}")

        user_message = await chat_repository.create_message(
            session_id=session_id,
            message=question,
            message_type="human",
            scenario="chat",
            source=0,
            status=0
        )
        logger.info(f"Created user message {user_message.id}")

        return session_id, messages, user_message.id
    
    async def _record_chat_messages(
        self, 
        session_id: str, 
        question: str, 
        ai_response: str, 
        message_id: int
    ) -> Optional[CloudbotAgentChatMessage]:
        """记录AI聊天消息到数据库
        
        Args:
            session_id: 会话ID
            question: 用户问题
            ai_response: AI回复内容
            message_id: 消息ID
            
        Returns:
            CloudbotAgentChatMessage: AI消息对象，如果没有AI回复则返回None
        """
        # 用户消息已经在 _handle_session_and_context 中创建了

        # 记录AI回复消息
        ai_message = None
        if ai_response:
            ai_message = await chat_repository.create_message(
                session_id=session_id,
                message=ai_response,
                message_type="ai",
                scenario="chat",
                source=0,
                message_id=message_id,
                status=0
            )
            logger.info(f"Created AI message {ai_message.id} with complete content for session {session_id}")
        else:
            logger.warning(f"No AI response content to record for session {session_id}")
        
        return ai_message
    
    # ==================== Chat方法的5个清晰阶段 ====================
    
    async def _prepare_session(
        self, 
        user_id: str, 
        session_id: Optional[str], 
        question: str, 
        messages: Optional[List[Dict[str, Any]]]
    ) -> tuple[str, Optional[List[Dict[str, Any]]], int]:
        """阶段1: 会话准备 - 处理会话管理和上下文加载"""
        return await self._handle_session_and_context(
            user_id, session_id, question, "", messages
        )
    
    async def _setup_execution(self, graph_type: str, message_id: int):
        """阶段2: 执行配置 - 创建Graph实例和配置"""
        langfuse_handler = CallbackHandler(
            public_key=get_config().observability.langfuse.public_key,
            secret_key=get_config().observability.langfuse.secret_key,
            host=get_config().observability.langfuse.endpoint
        )
        
        config = {
            "langfuse_handler": langfuse_handler,
            "thread_id": message_id
        }
        
        graph = self.graph_factory.create_graph(graph_type, config)
        logger.info(f"Graph configured with thread_id: {message_id}")
        return graph
    
    async def _stream_execution(
        self, 
        graph, 
        question: str, 
        messages: Optional[List[Dict[str, Any]]], 
        sse_parser: SSEMessageParser, 
        message_id: int
    ) -> AsyncGenerator[str, None]:
        """阶段3: 流式执行 - 执行Graph并实时输出SSE事件"""
        async for event in graph.astream(question=question, messages=messages):
            yield event  # 实时输出给客户端
            
            # 同时解析事件用于后续处理
            try:
                sse_parser.process_sse_event(event, str(message_id))
            except Exception as e:
                logger.debug(f"SSE event parsing failed: {e}")
                continue
    
    async def _parse_final_content(self, sse_parser: SSEMessageParser, session_id: str) -> str:
        """阶段4: 内容解析 - 从SSE事件中解析出最终答案"""
        final_answer = sse_parser.build_final_report()
        
        # 智能回退策略：如果没有reporter内容，尝试从planner获取
        if not final_answer.strip():
            collected_data = sse_parser.get_collected_data()
            planner_plan = collected_data.get("planner_plan")
            if planner_plan and isinstance(planner_plan, dict):
                final_answer = planner_plan.get("thought", "")
                logger.info(f"Using planner thought as fallback for session {session_id}")
        
        logger.info(f"Final answer parsed, length: {len(final_answer)}")
        return final_answer
    
    async def _persist_chat_data(
        self, 
        session_id: str, 
        question: str, 
        final_answer: str, 
        message_id: int
    ) -> None:
        """阶段5: 数据持久化 - 保存完整对话到数据库"""
        await self._record_chat_messages(session_id, question, final_answer, message_id)
        logger.info(f"Chat data persisted for session {session_id}")
    
    # ==================== 详细信息获取方法 ====================
    
    async def get_chat_detail(
        self,
        question: str, 
        user_id: str, 
        session_id: Optional[str] = None, 
        graph_type: str = "ReasoningAgent",
        messages: Optional[List[Dict[str, Any]]] = None,
    ) -> Dict[str, Any]:
        """
        获取聊天的详细结构化信息（类似task的detail）
        
        Args:
            question: 用户提出的问题
            user_id: 用户ID
            session_id: 会话ID，如果为空则创建新会话
            graph_type: 要使用的Graph类型，默认为 "ReasoningAgent"
            messages: 可选的消息列表
            
        Returns:
            Dict: 包含thought、plan、execution、final_answer等结构化信息
        """
        langfuse_handler = CallbackHandler(
            public_key=get_config().observability.langfuse.public_key,
            secret_key=get_config().observability.langfuse.secret_key,
            host=get_config().observability.langfuse.endpoint
        )

        try:
            # 1. 处理会话管理和上下文加载
            session_id, messages, message_id = await self._handle_session_and_context(
                user_id, session_id, question, "", messages
            )

            # 2. 生成跟踪标识
            thread_id = message_id
            logger.info(f"Generated thread_id {thread_id} and message_id {message_id}")

            # 3. 创建Graph实例并配置
            config = {
                "langfuse_handler": langfuse_handler,
                "thread_id": thread_id
            }
            graph = self.graph_factory.create_graph(graph_type, config)

            # 4. 执行对话并收集完整的结构化数据
            sse_parser = SSEMessageParser()
            
            async for event in graph.astream(question=question, messages=messages):
                # 只处理事件，不yield（因为这是获取详细信息的方法）
                try:
                    sse_parser.process_sse_event(event, str(message_id))
                except Exception as parse_error:
                    logger.debug(f"Failed to process SSE event: {parse_error}")
                    continue

            # 5. 构建结构化详细信息
            structured_detail = sse_parser.build_structured_detail()
            final_answer = sse_parser.build_final_report()
            
            # 如果没有reporter内容，尝试从planner获取
            if not final_answer.strip():
                collected_data = sse_parser.get_collected_data()
                if collected_data.get("planner_plan") and isinstance(collected_data["planner_plan"], dict):
                    final_answer = collected_data["planner_plan"].get("thought", "")
                    structured_detail["final_answer"] = final_answer
            
            # 6. 记录完整的对话消息到数据库
            await self._record_chat_messages(
                session_id, question, final_answer, message_id
            )
            
            logger.info(f"Chat detail generated for session {session_id}, final answer length: {len(final_answer)}")
            return structured_detail
                
        except Exception as e:
            logger.error(f"Error in get_chat_detail method: {e}")
            # 返回错误信息的结构化格式
            return {
                "thought": "",
                "plan": [],
                "execution": [],
                "final_answer": f"处理过程中发生错误: {str(e)}",
                "urls": []
            }
    
    async def chat(
        self, 
        question: str, 
        user_id: str, 
        session_id: Optional[str] = None, 
        graph_type: str = "ReasoningAgent",
        messages: Optional[List[Dict[str, Any]]] = None,
    ) -> AsyncGenerator[str, None]:
        """
        聊天服务的核心方法 - 5个清晰阶段
        
        阶段1: 会话准备 -> 阶段2: 执行配置 -> 阶段3: 流式执行 -> 阶段4: 内容解析 -> 阶段5: 数据持久化

        Args:
            question: 用户提出的问题
            user_id: 用户ID  
            session_id: 会话ID，如果为空则创建新会话
            graph_type: Graph类型，默认为 "ReasoningAgent"
            messages: 可选的消息列表

        Yields:
            str: Graph流式返回的SSE事件
        """
        try:
            # ==================== 阶段1: 会话准备 ====================
            session_id, messages, message_id = await self._prepare_session(
                user_id, session_id, question, messages
            )
            
            # ==================== 阶段2: 执行配置 ====================
            graph = await self._setup_execution(graph_type, message_id)
            
            # ==================== 阶段3: 流式执行 ====================
            sse_parser = SSEMessageParser()
            async for event in self._stream_execution(graph, question, messages, sse_parser, message_id):
                yield event
            
            # ==================== 阶段4: 内容解析 ====================
            final_answer = await self._parse_final_content(sse_parser, session_id)
            
            # ==================== 阶段5: 数据持久化 ====================
            await self._persist_chat_data(session_id, question, final_answer, message_id)
                
        except Exception as e:
            logger.error(f"Chat execution failed: {e}")
            raise
    


def create_chat_service() -> ChatService:
    """创建聊天服务实例
        
    Returns:
        ChatService实例
    """
    return ChatService()