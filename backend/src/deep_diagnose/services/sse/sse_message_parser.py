"""
简化的SSE消息解析器 - 追求简洁和实用
"""

import json
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field

from deep_diagnose.common.utils.json_utils import repair_json_output

logger = logging.getLogger(__name__)


@dataclass
class ToolExecution:
    """工具执行信息"""
    tool_id: str
    name: str
    agent: str
    thread_id: str
    args_chunks: List[str] = field(default_factory=list)
    result: str = ""

    @property
    def combined_args(self) -> str:
        return "".join(self.args_chunks)

    @property
    def parsed_args(self) -> Dict[str, Any]:
        if not self.combined_args.strip():
            return {}
        try:
            repaired_json = repair_json_output(self.combined_args)
            return json.loads(repaired_json)
        except json.JSONDecodeError:
            return {}

    @property
    def parsed_result(self) -> Any:
        if not self.result.strip():
            return None
        try:
            return json.loads(self.result)
        except json.JSONDecodeError:
            return self.result


@dataclass
class ParsedData:
    """解析后的数据"""
    planner_chunks: List[str] = field(default_factory=list)
    planner_plan: Optional[Dict[str, Any]] = None
    researcher_data: List[Dict[str, str]] = field(default_factory=list)
    report_contents: List[str] = field(default_factory=list)
    tool_executions: Dict[str, ToolExecution] = field(default_factory=dict)
    thread_to_tool: Dict[str, str] = field(default_factory=dict)  # 直接内置状态跟踪


class SSEMessageParser:
    """简化的SSE消息解析器"""

    def __init__(self):
        self.data = ParsedData()

    def process_sse_event(self, sse_event: str, task_id: str) -> bool:
        """处理SSE事件"""
        event_data = self._parse_sse_event(sse_event)
        if not event_data:
            return False

        try:
            return self._handle_event(event_data, task_id)
        except Exception as e:
            logger.error(f"Task {task_id}: Error processing event: {e}")
            return False

    def _parse_sse_event(self, sse_event: str) -> Optional[Dict[str, Any]]:
        """解析SSE事件"""
        try:
            lines = sse_event.strip().split('\n')
            event_type = None
            data_str = None

            for line in lines:
                if line.startswith('event: '):
                    event_type = line[7:].strip()
                elif line.startswith('data: '):
                    data_str = line[6:].strip()

            if data_str:
                data = json.loads(data_str)
                # 推断事件类型
                if not event_type:
                    if "tool_calls" in data:
                        event_type = "tool_calls"
                    elif "tool_call_chunks" in data:
                        event_type = "tool_call_chunks"
                    elif "tool_call_id" in data:
                        event_type = "tool_call_result"
                    else:
                        event_type = "message_chunk"
                
                return {"type": event_type, "data": data}
            
            return None
        except (json.JSONDecodeError, Exception) as e:
            logger.warning(f"Failed to parse SSE event: {e}")
            return None

    def _handle_event(self, event_data: Dict[str, Any], task_id: str) -> bool:
        """处理事件"""
        event_type = event_data["type"]
        data = event_data["data"]

        if event_type == "message_chunk":
            return self._handle_message_chunk(data, task_id)
        elif event_type == "tool_calls":
            return self._handle_tool_calls(data, task_id)
        elif event_type == "tool_call_chunks":
            return self._handle_tool_chunks(data, task_id)
        elif event_type == "tool_call_result":
            return self._handle_tool_result(data, task_id)
        
        return False

    def _handle_message_chunk(self, data: Dict[str, Any], task_id: str) -> bool:
        """处理消息块"""
        agent = data.get("agent", "")
        content = data.get("content", "")
        finish_reason = data.get("finish_reason")

        if not content.strip() and not finish_reason:
            return False

        if agent == "planner":
            self.data.planner_chunks.append(content)
            logger.debug(f"Task {task_id}: Collected planner chunk ({len(content)} chars)")
            
            # 先返回内容添加的结果
            content_added = bool(content.strip())
            
            # 如果finish_reason是stop，解析完整的planner JSON
            if finish_reason == "stop":
                self._parse_planner_plan(task_id)
                return True  # finish_reason=stop时总是返回True
            
            return content_added
        
        elif agent == "reporter":
            self.data.report_contents.append(content)
            return bool(content.strip())
        
        elif agent in ["researcher", "coder"]:
            self.data.researcher_data.append({"agent": agent, "content": content})
            return len(content.strip()) > 50
        
        return False

    def _handle_tool_calls(self, data: Dict[str, Any], task_id: str) -> bool:
        """处理工具调用"""
        tool_calls = data.get("tool_calls", [])
        if not tool_calls:
            return False

        thread_id = data.get("thread_id", "")
        for tool_call in tool_calls:
            tool_id = tool_call.get("id")
            if tool_id:
                execution = ToolExecution(
                    tool_id=tool_id,
                    name=tool_call.get("name", ""),
                    agent=data.get("agent", ""),
                    thread_id=thread_id
                )
                self.data.tool_executions[tool_id] = execution
                if thread_id:
                    self.data.thread_to_tool[thread_id] = tool_id
                logger.info(f"Task {task_id}: Registered tool: {execution.name}")

        # 同时处理可能存在的chunks
        if "tool_call_chunks" in data:
            self._handle_tool_chunks(data, task_id)
        
        return True

    def _handle_tool_chunks(self, data: Dict[str, Any], task_id: str) -> bool:
        """处理工具参数块"""
        tool_call_chunks = data.get("tool_call_chunks", [])
        if not tool_call_chunks:
            return False

        # 查找当前工具ID
        thread_id = data.get("thread_id")
        current_tool_id = self.data.thread_to_tool.get(thread_id) if thread_id else None
        
        for chunk in tool_call_chunks:
            chunk_tool_id = chunk.get("id") or current_tool_id
            if chunk_tool_id and chunk_tool_id in self.data.tool_executions:
                args_value = chunk.get("args", "")
                args_str = json.dumps(args_value, ensure_ascii=False) if isinstance(args_value, dict) else str(args_value or "")
                self.data.tool_executions[chunk_tool_id].args_chunks.append(args_str)
        
        return False  # chunks不触发更新

    def _handle_tool_result(self, data: Dict[str, Any], task_id: str) -> bool:
        """处理工具结果"""
        tool_call_id = data.get("tool_call_id")
        content = data.get("content", "")
        
        if tool_call_id and tool_call_id in self.data.tool_executions:
            self.data.tool_executions[tool_call_id].result = content
            logger.info(f"Task {task_id}: Got result for {tool_call_id}")
            return True
        
        return False

    def _parse_planner_plan(self, task_id: str):
        """解析planner计划"""
        if not self.data.planner_chunks:
            logger.warning(f"Task {task_id}: Attempted to parse planner JSON, but no chunks were collected.")
            return

        combined_content = "".join(self.data.planner_chunks)
        logger.info(f"Task {task_id}: Attempting to parse complete planner JSON ({len(combined_content)} chars).")
        try:
            repaired_json = repair_json_output(combined_content)
            parsed_plan = json.loads(repaired_json)
            if "thought" in parsed_plan and "steps" in parsed_plan:
                self.data.planner_plan = parsed_plan
                logger.info(f"Task {task_id}: Successfully parsed complete planner plan with {len(parsed_plan.get('steps', []))} steps.")
                logger.info(f"Task {task_id}: Planner plan steps: {[step.get('title', '') for step in parsed_plan.get('steps', [])]}")
            else:
                logger.warning(f"Task {task_id}: Parsed planner JSON is missing 'thought' or 'steps'.")
        except Exception as e:
            logger.error(f"Task {task_id}: Failed to parse complete planner JSON. Error: {e}")
            logger.debug(f"Task {task_id}: Faulty planner content: {combined_content}")

    def build_final_report(self, contents: Optional[List[str]] = None) -> str:
        """构建最终报告"""
        contents = contents or self.data.report_contents
        return "".join(contents) if contents else ""

    def build_structured_detail(self, html_path: Optional[str] = None) -> Dict[str, Any]:
        """构建结构化详情"""
        detail = {
            "thought": "",
            "plan": [],
            "execution": [],
            "final_answer": "",
            "urls": []
        }

        # 构建计划
        if self.data.planner_plan and isinstance(self.data.planner_plan, dict):
            detail["thought"] = self.data.planner_plan.get("thought", "")
            logger.info(f"Building structured detail - extracted thought: {detail['thought'][:100]}...")

            steps = self.data.planner_plan.get("steps", [])
            logger.info(f"Building structured detail - found {len(steps)} steps in planner_plan")
            for i, step in enumerate(steps, 1):
                plan_item = {
                    "step": i,
                    "title": step.get("title", ""),
                    "description": step.get("description", "")
                }
                detail["plan"].append(plan_item)
                logger.debug(f"Building structured detail - added plan step {i}: {step.get('title', '')}")

        # 构建执行信息
        excluded_tools = {"handoff_to_planner", "handoff_to_coordinator", "internal_handoff"}
        for execution in self.data.tool_executions.values():
            if execution.name.lower() not in excluded_tools:
                detail["execution"].append(self._build_execution_info(execution))

        # 最终答案
        if self.data.report_contents:
            detail["final_answer"] = "".join(self.data.report_contents)

        # URL
        if html_path:
            detail["urls"].append({
                "name": "CloudBot智能体-长推理诊断报告",
                "url": html_path
            })

        return detail

    def _build_execution_info(self, execution: ToolExecution) -> Dict[str, Any]:
        """构建执行信息"""
        parameters = execution.parsed_args
        result = execution.parsed_result
        
        # 简单的任务描述
        param_desc = ""
        if parameters:
            if len(parameters) == 1:
                key, value = next(iter(parameters.items()))
                if isinstance(value, list) and value:
                    param_desc = f"{key}={value[0]}"
                elif isinstance(value, (str, int, float)):
                    param_desc = f"{key}={value}"
                else:
                    param_desc = f"{key}=数据"
            else:
                param_desc = f"{len(parameters)}个参数"

        task_name = f"调用{execution.name}工具"
        if param_desc:
            task_name += f"({param_desc}"
            if isinstance(result, list):
                task_name += f", 返回{len(result)}条记录"
            elif isinstance(result, dict):
                task_name += f", 返回结构化数据"
            task_name += ")"
        elif isinstance(result, list):
            task_name += f"(返回{len(result)}条记录)"

        # 简单的摘要
        summary_parts = [f"执行{execution.name}"]
        if parameters:
            summary_parts.append(f"参数{len(parameters)}个")
        if isinstance(result, list):
            summary_parts.append(f"成功返回{len(result)}条记录")
        elif result:
            summary_parts.append("执行成功")
        else:
            summary_parts.append("执行完成")

        return {
            "task_name": task_name,
            "tool_name": execution.name,
            "parameters": parameters,
            "result": {
                "status": "success" if result else "error",
                "output": result or execution.result,
                "summary": "，".join(summary_parts)
            },
            "execution_time": 0.0
        }

    def get_collected_data(self) -> Dict[str, Any]:
        """兼容旧接口"""
        tool_calls = []
        tool_call_chunks = []
        tool_call_results = []
        tool_calls_data = []

        for execution in self.data.tool_executions.values():
            tool_calls.append({
                "id": execution.tool_id,
                "name": execution.name,
                "agent": execution.agent,
                "thread_id": execution.thread_id
            })
            
            for i, chunk in enumerate(execution.args_chunks):
                tool_call_chunks.append({
                    "id": execution.tool_id,
                    "args": chunk,
                    "agent": execution.agent,
                    "thread_id": execution.thread_id,
                    "index": i
                })
            
            if execution.result:
                tool_call_results.append({
                    "id": f"result_{execution.tool_id}",
                    "tool_call_id": execution.tool_id,
                    "content": execution.result,
                    "agent": execution.agent,
                    "thread_id": execution.thread_id
                })
                tool_calls_data.append(execution.result)

        return {
            "planner_chunks": self.data.planner_chunks,
            "planner_plan": self.data.planner_plan,
            "researcher_data": self.data.researcher_data,
            "report_contents": self.data.report_contents,
            "tool_calls_data": tool_calls_data,
            "tool_calls": tool_calls,
            "tool_call_chunks": tool_call_chunks,
            "tool_call_results": tool_call_results
        }

    # 兼容方法
    def parse_sse_event(self, sse_event: str) -> Optional[Dict[str, Any]]:
        """兼容旧接口"""
        try:
            lines = sse_event.strip().split('\n')
            for line in lines:
                if line.startswith('data: '):
                    return json.loads(line[6:].strip())
            return None
        except:
            return None