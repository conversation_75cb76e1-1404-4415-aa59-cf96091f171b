"""
重构后的SSE消息解析模块
采用模块化设计，提高代码可读性和可维护性
"""

import json
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum

from deep_diagnose.common.utils.json_utils import repair_json_output

logger = logging.getLogger(__name__)


class EventType(Enum):
    """SSE事件类型枚举"""
    MESSAGE_CHUNK = "message_chunk"
    TOOL_CALLS = "tool_calls"
    TOOL_CALL_CHUNKS = "tool_call_chunks"
    TOOL_CALL_RESULT = "tool_call_result"


class AgentType(Enum):
    """智能体类型枚举"""
    PLANNER = "planner"
    RESEARCHER = "researcher"
    CODER = "coder"
    REPORTER = "reporter"
    COORDINATOR = "coordinator"


@dataclass
class ToolExecution:
    """工具执行信息"""
    tool_id: str
    name: str
    agent: str
    args_chunks: List[str] = field(default_factory=list)
    result: str = ""
    thread_id: str = ""

    @property
    def combined_args(self) -> str:
        """合并参数块"""
        return "".join(self.args_chunks)

    @property
    def parsed_args(self) -> Dict[str, Any]:
        """解析参数"""
        if not self.combined_args.strip():
            return {}
        try:
            repaired_json = repair_json_output(self.combined_args)
            return json.loads(repaired_json)
        except json.JSONDecodeError:
            return {}

    @property
    def parsed_result(self) -> Any:
        """解析结果"""
        if not self.result.strip():
            return None
        try:
            return json.loads(self.result)
        except json.JSONDecodeError:
            return self.result


@dataclass
class CollectedData:
    """收集的数据容器 - 纯数据存储"""
    planner_chunks: List[str] = field(default_factory=list)
    planner_plan: Optional[Dict[str, Any]] = None
    researcher_data: List[Dict[str, str]] = field(default_factory=list)
    report_contents: List[str] = field(default_factory=list)
    tool_executions: Dict[str, ToolExecution] = field(default_factory=dict)


class DataTransformer:
    """数据格式转换器 - 负责各种格式转换"""
    
    @staticmethod
    def to_legacy_format(data: CollectedData) -> Dict[str, Any]:
        """转换为兼容旧格式的数据结构"""
        tool_calls = []
        tool_call_chunks = []
        tool_call_results = []
        tool_calls_data = []

        for execution in data.tool_executions.values():
            tool_calls.append({
                "id": execution.tool_id,
                "name": execution.name,
                "agent": execution.agent,
                "thread_id": execution.thread_id
            })
            
            for i, chunk in enumerate(execution.args_chunks):
                tool_call_chunks.append({
                    "id": execution.tool_id,
                    "args": chunk,
                    "agent": execution.agent,
                    "thread_id": execution.thread_id,
                    "index": i
                })
            
            if execution.result:
                tool_call_results.append({
                    "id": f"result_{execution.tool_id}",
                    "tool_call_id": execution.tool_id,
                    "content": execution.result,
                    "agent": execution.agent,
                    "thread_id": execution.thread_id
                })
                tool_calls_data.append(execution.result)

        return {
            "planner_chunks": data.planner_chunks,
            "planner_plan": data.planner_plan,
            "researcher_data": data.researcher_data,
            "report_contents": data.report_contents,
            "tool_calls_data": tool_calls_data,
            "tool_calls": tool_calls,
            "tool_call_chunks": tool_call_chunks,
            "tool_call_results": tool_call_results
        }


class SSEEventParser:
    """SSE事件解析器"""

    @staticmethod
    def parse_sse_line(sse_event: str) -> Optional[Dict[str, Any]]:
        """解析SSE事件行"""
        try:
            lines = sse_event.strip().split('\n')
            event_type = None
            data_str = None

            for line in lines:
                if line.startswith('event: '):
                    event_type = line[7:].strip()
                elif line.startswith('data: '):
                    data_str = line[6:].strip()

            if data_str:
                try:
                    data = json.loads(data_str)
                    # 如果没有明确的事件类型，尝试推断
                    if not event_type:
                        event_type = SSEEventParser._infer_event_type(data)
                    
                    return {
                        "type": event_type,
                        "data": data
                    }
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse SSE data as JSON: {data_str}")
            
            return None
        except Exception as e:
            logger.warning(f"Failed to parse SSE event: {e}")
            return None

    @staticmethod
    def _infer_event_type(data: Dict[str, Any]) -> str:
        """推断事件类型"""
        if "tool_calls" in data:
            return EventType.TOOL_CALLS.value
        elif "tool_call_chunks" in data:
            return EventType.TOOL_CALL_CHUNKS.value
        elif "tool_call_id" in data:
            return EventType.TOOL_CALL_RESULT.value
        else:
            return EventType.MESSAGE_CHUNK.value


class ContentProcessor:
    """内容处理器"""

    def __init__(self, data: CollectedData):
        self.data = data

    def process_planner_content(self, content: str, finish_reason: Optional[str], task_id: str) -> bool:
        """处理planner内容"""
        content_added = False
        
        if content.strip():
            self.data.planner_chunks.append(content)
            logger.debug(f"Task {task_id}: Collected planner chunk ({len(content)} chars)")
            content_added = True
        
        if finish_reason == "stop":
            self._parse_complete_planner_json(task_id)
            return True
        
        return content_added

    def process_agent_content(self, agent: str, content: str, task_id: str) -> bool:
        """处理researcher/coder内容"""
        if not content.strip():
            return False
        
        if agent == AgentType.REPORTER.value:
            processed_content = content.replace('\n', '\n')
            self.data.report_contents.append(processed_content)
            logger.debug(f"Task {task_id}: Collected reporter content ({len(processed_content)} chars)")
            return len(processed_content.strip()) > 30
        else:
            self.data.researcher_data.append({"agent": agent, "content": content})
            logger.debug(f"Task {task_id}: Collected {agent} content ({len(content)} chars)")
            return len(content.strip()) > 50

    def _parse_complete_planner_json(self, task_id: str):
        """解析完整的planner JSON"""
        if not self.data.planner_chunks:
            logger.warning(f"Task {task_id}: No planner chunks to parse")
            return

        combined_content = "".join(self.data.planner_chunks)
        logger.info(f"Task {task_id}: Parsing complete planner JSON ({len(combined_content)} chars)")
        
        try:
            repaired_json = repair_json_output(combined_content)
            parsed_plan = json.loads(repaired_json)

            if "thought" in parsed_plan and "steps" in parsed_plan:
                self.data.planner_plan = parsed_plan
                logger.info(f"Task {task_id}: Successfully parsed planner plan with {len(parsed_plan.get('steps', []))} steps")
            else:
                logger.warning(f"Task {task_id}: Parsed planner JSON missing required fields")

        except (json.JSONDecodeError, Exception) as e:
            logger.error(f"Task {task_id}: Failed to parse planner JSON: {e}")


class ToolExecutionTracker:
    """工具执行状态跟踪器"""
    
    def __init__(self):
        self._thread_to_tool: Dict[str, str] = {}
    
    def register_tool_for_thread(self, thread_id: str, tool_id: str) -> None:
        """注册线程对应的工具ID"""
        self._thread_to_tool[thread_id] = tool_id
    
    def get_current_tool_for_thread(self, thread_id: str) -> Optional[str]:
        """获取线程当前的工具ID"""
        return self._thread_to_tool.get(thread_id)


class ToolExecutionManager:
    """工具执行管理器"""

    def __init__(self, data: CollectedData):
        self.data = data
        self.tracker = ToolExecutionTracker()

    def process_tool_calls(self, tool_calls_data: Dict[str, Any], task_id: str) -> bool:
        """处理工具调用声明"""
        tool_calls = tool_calls_data.get("tool_calls", [])
        if not tool_calls:
            return False

        thread_id = tool_calls_data.get("thread_id", "")
        for tool_call in tool_calls:
            tool_id = tool_call.get("id")
            if tool_id:
                execution = ToolExecution(
                    tool_id=tool_id,
                    name=tool_call.get("name", ""),
                    agent=tool_calls_data.get("agent", ""),
                    thread_id=thread_id
                )
                self.data.tool_executions[tool_id] = execution
                # 注册线程和工具的映射关系
                if thread_id:
                    self.tracker.register_tool_for_thread(thread_id, tool_id)
                logger.info(f"Task {task_id}: Registered tool call: {execution.name}")
        
        return True
    
    def process_mixed_tool_event(self, tool_calls_data: Dict[str, Any], task_id: str) -> bool:
        """处理包含tool_calls和tool_call_chunks的混合事件"""
        result = False
        
        # 先处理tool_calls
        if "tool_calls" in tool_calls_data:
            result = self.process_tool_calls(tool_calls_data, task_id)
        
        # 再处理tool_call_chunks
        if "tool_call_chunks" in tool_calls_data:
            self.process_tool_chunks(tool_calls_data, task_id)
        
        return result

    def process_tool_chunks(self, chunks_data: Dict[str, Any], task_id: str) -> bool:
        """处理工具参数块"""
        tool_call_chunks = chunks_data.get("tool_call_chunks", [])
        if not tool_call_chunks:
            return False

        # 找到当前活跃的工具调用ID
        current_tool_id = self._find_current_tool_id(chunks_data)
        chunks_added = False
        
        for chunk in tool_call_chunks:
            chunk_tool_id = chunk.get("id") or current_tool_id
            if chunk_tool_id and chunk_tool_id in self.data.tool_executions:
                args_value = chunk.get("args", "")
                if isinstance(args_value, dict):
                    args_str = json.dumps(args_value, ensure_ascii=False)
                else:
                    args_str = str(args_value) if args_value is not None else ""
                
                self.data.tool_executions[chunk_tool_id].args_chunks.append(args_str)
                logger.debug(f"Task {task_id}: Added args chunk to {chunk_tool_id}: {len(args_str)} chars")
                chunks_added = True
        
        # 根据原始逻辑，tool_call_chunks不触发立即更新
        return False

    def process_tool_result(self, result_data: Dict[str, Any], task_id: str) -> bool:
        """处理工具执行结果"""
        tool_call_id = result_data.get("tool_call_id")
        content = result_data.get("content", "")
        
        if tool_call_id and tool_call_id in self.data.tool_executions:
            self.data.tool_executions[tool_call_id].result = content
            logger.info(f"Task {task_id}: Set result for {tool_call_id}: {len(content)} chars")
            return True
        
        return False

    def _find_current_tool_id(self, chunks_data: Dict[str, Any]) -> Optional[str]:
        """查找当前活跃的工具调用ID"""
        thread_id = chunks_data.get("thread_id")
        if thread_id:
            return self.tracker.get_current_tool_for_thread(thread_id)
        return None


class ReportBuilder:
    """报告构建器"""

    def __init__(self, data: CollectedData):
        self.data = data

    def build_final_report(self, contents: Optional[List[str]] = None) -> str:
        """构建最终报告"""
        if contents is None:
            contents = self.data.report_contents

        if not contents:
            return ""

        combined_content = "".join(contents)
        logger.debug(f"Built report from {len(contents)} chunks, total length: {len(combined_content)}")
        return combined_content

    def build_structured_detail(self, html_path: Optional[str] = None) -> Dict[str, Any]:
        """构建结构化详情"""
        detail = {
            "thought": "",
            "plan": [],
            "execution": [],
            "final_answer": "",
            "urls": []
        }

        # 构建思考和计划
        if self.data.planner_plan:
            detail["thought"] = self.data.planner_plan.get("thought", "")
            steps = self.data.planner_plan.get("steps", [])
            
            for i, step in enumerate(steps, 1):
                detail["plan"].append({
                    "step": i,
                    "title": step.get("title", ""),
                    "description": step.get("description", "")
                })

        # 构建执行信息
        for execution in self.data.tool_executions.values():
            if self._should_include_in_execution(execution):
                detail["execution"].append(self._build_execution_info(execution))

        # 构建最终答案
        if self.data.report_contents:
            detail["final_answer"] = "".join(self.data.report_contents)

        # 添加URL
        if html_path:
            detail["urls"].append({
                "name": "CloudBot智能体-长推理诊断报告",
                "url": html_path
            })

        return detail
    
    def _should_include_in_execution(self, execution: ToolExecution) -> bool:
        """判断工具执行是否应该包含在执行列表中"""
        # 可配置的排除列表
        excluded_tools = {
            "handoff_to_planner",
            "handoff_to_coordinator", 
            "internal_handoff"
        }
        return execution.name.lower() not in excluded_tools

    def _build_execution_info(self, execution: ToolExecution) -> Dict[str, Any]:
        """构建单个工具执行信息"""
        parameters = execution.parsed_args
        result = execution.parsed_result
        
        task_description = self._generate_task_description(execution.name, parameters, result)
        summary = self._generate_summary(execution.name, parameters, result)

        return {
            "task_name": task_description,
            "tool_name": execution.name,
            "parameters": parameters,
            "result": {
                "status": "success" if result else "error",
                "output": result or execution.result,
                "summary": summary
            },
            "execution_time": 0.0
        }

    def _generate_task_description(self, tool_name: str, parameters: Dict[str, Any], result: Any) -> str:
        """生成任务描述"""
        base_desc = f"调用{tool_name}工具"
        param_parts = []
        
        # 通用参数描述 - 不预设任何特定字段
        param_summary = self._summarize_parameters(parameters)
        if param_summary:
            param_parts.append(param_summary)
        
        # 结果统计
        if isinstance(result, list):
            param_parts.append(f"返回{len(result)}条记录")
        elif isinstance(result, dict):
            param_parts.append("返回结构化数据")

        return f"{base_desc}({', '.join(param_parts)})" if param_parts else base_desc
    
    def _summarize_parameters(self, parameters: Dict[str, Any]) -> str:
        """通用参数摘要 - 不预设任何特定字段类型"""
        if not parameters:
            return ""
        
        # 简单统计参数数量和类型
        param_count = len(parameters)
        if param_count == 0:
            return ""
        elif param_count == 1:
            key, value = next(iter(parameters.items()))
            return self._format_single_parameter(key, value)
        else:
            return f"{param_count}个参数"
    
    def _format_single_parameter(self, key: str, value: Any) -> str:
        """格式化单个参数"""
        if isinstance(value, list):
            if len(value) == 1:
                return f"{key}={value[0]}"
            else:
                return f"{key}={len(value)}项"
        elif isinstance(value, (str, int, float)):
            return f"{key}={value}"
        elif isinstance(value, dict):
            return f"{key}=结构化数据"
        else:
            return f"{key}={type(value).__name__}"

    def _generate_summary(self, tool_name: str, parameters: Dict[str, Any], result: Any) -> str:
        """生成执行摘要"""
        summary_parts = [f"执行{tool_name}"]
        
        # 简单参数统计
        if parameters:
            summary_parts.append(f"参数{len(parameters)}个")

        # 结果摘要
        result_summary = self._generate_result_summary(result)
        summary_parts.append(result_summary)

        return "，".join(summary_parts)
    
    def _generate_result_summary(self, result: Any) -> str:
        """生成结果摘要"""
        if isinstance(result, list):
            return f"成功返回{len(result)}条记录"
        elif isinstance(result, dict):
            return "成功返回结构化数据"
        elif result:
            return "执行成功"
        else:
            return "执行完成"


class SSEMessageParser:
    """重构后的SSE消息解析器 - 主入口类"""

    def __init__(self):
        self.data = CollectedData()
        self.content_processor = ContentProcessor(self.data)
        self.tool_manager = ToolExecutionManager(self.data)
        self.report_builder = ReportBuilder(self.data)

    def process_sse_event(self, sse_event: str, task_id: str) -> bool:
        """处理单个SSE事件"""
        event_data = SSEEventParser.parse_sse_line(sse_event)
        if not event_data:
            return False

        event_type = event_data.get("type")
        data = event_data.get("data", {})
        
        try:
            return self._dispatch_event(event_type, data, task_id)
        except Exception as e:
            logger.error(f"Task {task_id}: Error processing event {event_type}: {e}")
            return False

    def _dispatch_event(self, event_type: str, data: Dict[str, Any], task_id: str) -> bool:
        """分发事件到相应的处理器"""
        if event_type == EventType.MESSAGE_CHUNK.value:
            return self._handle_message_chunk(data, task_id)
        elif event_type == EventType.TOOL_CALLS.value:
            # 检查是否为混合事件（同时包含tool_calls和tool_call_chunks）
            if "tool_call_chunks" in data:
                return self.tool_manager.process_mixed_tool_event(data, task_id)
            else:
                return self.tool_manager.process_tool_calls(data, task_id)
        elif event_type == EventType.TOOL_CALL_CHUNKS.value:
            return self.tool_manager.process_tool_chunks(data, task_id)
        elif event_type == EventType.TOOL_CALL_RESULT.value:
            return self.tool_manager.process_tool_result(data, task_id)
        else:
            logger.debug(f"Task {task_id}: Unknown event type: {event_type}")
            return False

    def _handle_message_chunk(self, data: Dict[str, Any], task_id: str) -> bool:
        """处理消息块事件"""
        agent = data.get("agent", "")
        content = data.get("content", "")
        finish_reason = data.get("finish_reason")

        if agent == AgentType.PLANNER.value:
            return self.content_processor.process_planner_content(content, finish_reason, task_id)
        elif agent in [AgentType.RESEARCHER.value, AgentType.CODER.value, AgentType.REPORTER.value]:
            return self.content_processor.process_agent_content(agent, content, task_id)
        
        return False

    def get_collected_data(self) -> Dict[str, Any]:
        """获取收集到的数据（兼容旧接口）"""
        return DataTransformer.to_legacy_format(self.data)

    def build_final_report(self, contents: Optional[List[str]] = None) -> str:
        """构建最终报告"""
        return self.report_builder.build_final_report(contents)

    def build_structured_detail(self, html_path: Optional[str] = None) -> Dict[str, Any]:
        """构建结构化详情"""
        return self.report_builder.build_structured_detail(html_path)

    # 兼容旧接口的方法
    def parse_sse_event(self, sse_event: str) -> Optional[Dict[str, Any]]:
        """解析SSE事件（兼容旧接口）"""
        try:
            lines = sse_event.strip().split('\n')
            data_str = None
            for line in lines:
                if line.startswith('data: '):
                    data_str = line[6:].strip()
                    break
            
            if data_str:
                return json.loads(data_str)
            return None
        except (json.JSONDecodeError, Exception):
            return None
    
    def _parse_sse_line(self, sse_event: str) -> Optional[Dict[str, Any]]:
        """解析SSE事件行（兼容旧接口）"""
        return self.parse_sse_event(sse_event)