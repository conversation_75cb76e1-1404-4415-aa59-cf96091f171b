"""
SSE消息解析模块
负责解析工作流SSE事件并构建结构化数据
"""

import json
import logging
from typing import Dict, List, Any, Optional

from deep_diagnose.common.utils.json_utils import repair_json_output

logger = logging.getLogger(__name__)


class SSEMessageParser:
    """SSE消息解析器"""

    def __init__(self):
        self.data_collector = {
            "planner_chunks": [],
            "planner_plan": None,
            "researcher_data": [],
            "tool_calls_data": [],
            "report_contents": [],
            # 结构化工具调用数据
            "tool_calls": [],
            "tool_call_chunks": [],
            "tool_call_results": []
        }

    def _parse_sse_line(self, sse_event: str) -> Optional[Dict[str, Any]]:
        """
        解析SSE事件的行
        """
        try:
            lines = sse_event.strip().split('\n')
            event_type = None
            data_str = None

            for line in lines:
                if line.startswith('event: '):
                    event_type = line[7:].strip()
                elif line.startswith('data: '):
                    data_str = line[6:].strip()

            if event_type and data_str:
                try:
                    # The data might be a string representation of a dict, not a JSON object.
                    # Example: '{"thread_id": "...", "agent": "..."}'
                    data = json.loads(data_str)
                    return {
                        "type": event_type,
                        "data": data
                    }
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse SSE data as JSON: {data_str}")
                    return None
            
            # Handle cases where the event might not be fully formed but contains data
            if data_str:
                 try:
                    data = json.loads(data_str)
                    # Infer type if missing
                    inferred_type = "message_chunk" # A reasonable default
                    if "tool_calls" in data:
                        inferred_type = "tool_calls"
                    elif "tool_call_chunks" in data:
                        inferred_type = "tool_call_chunks"
                    elif "tool_call_id" in data:
                         inferred_type = "tool_call_result"

                    return {
                        "type": inferred_type,
                        "data": data
                    }
                 except json.JSONDecodeError:
                    pass

            return None
        except Exception as e:
            logger.warning(f"Failed to parse SSE event: {e}")
            return None

    def process_sse_event(self, sse_event: str, task_id: str) -> bool:
        """
        处理单个SSE事件
        """
        event_data = self._parse_sse_line(sse_event)
        if not event_data:
            return False

        event_type = event_data.get("type")
        data = event_data.get("data", {})
        agent = data.get("agent", "")
        content = data.get("content", "")
        finish_reason = data.get("finish_reason")

        content_added = False

        if event_type == "message_chunk":
            if agent == "planner":
                if content:
                    content_added = self._process_planner_content(content, task_id)
                if finish_reason == "stop":
                    self._parse_complete_planner_json(task_id)
                    content_added = True
            elif content:
                if agent in ["researcher", "coder"]:
                    content_added = self._process_researcher_content(agent, content, task_id)
                elif agent == "reporter":
                    content_added = self._process_reporter_content(content, task_id)

        elif event_type == "tool_calls":
            self._process_tool_call_chunks_event(data, task_id)
            content_added = self._process_tool_calls_event(data, task_id)
        elif event_type == "tool_call_chunks":
            content_added = self._process_tool_call_chunks_event(data, task_id)
        elif event_type == "tool_call_result":
            content_added = self._process_tool_call_result_event(data, task_id)

        return content_added

    def _parse_complete_planner_json(self, task_id: str):
        """Parses the complete JSON from collected planner chunks."""
        if not self.data_collector["planner_chunks"]:
            logger.warning(f"Task {task_id}: Attempted to parse planner JSON, but no chunks were collected.")
            return

        combined_content = "".join(self.data_collector["planner_chunks"])
        logger.info(f"Task {task_id}: Attempting to parse complete planner JSON ({len(combined_content)} chars).")
        try:
            repaired_json = repair_json_output(combined_content)
            parsed_plan = json.loads(repaired_json)

            if "thought" in parsed_plan and "steps" in parsed_plan:
                self.data_collector["planner_plan"] = parsed_plan
                logger.info(f"Task {task_id}: Successfully parsed complete planner plan with {len(parsed_plan.get('steps', []))} steps.")
                logger.info(f"Task {task_id}: Planner plan steps: {[step.get('title', '') for step in parsed_plan.get('steps', [])]}")
            else:
                logger.warning(f"Task {task_id}: Parsed planner JSON is missing 'thought' or 'steps'.")

        except (json.JSONDecodeError, Exception) as e:
            logger.error(f"Task {task_id}: Failed to parse complete planner JSON. Error: {e}")
            logger.debug(f"Task {task_id}: Faulty planner content: {combined_content}")

    def _process_planner_content(self, content: str, task_id: str) -> bool:
        """处理planner内容 - now only appends chunks."""
        if content.strip():
            self.data_collector["planner_chunks"].append(content)
            logger.debug(f"Task {task_id}: Collected planner chunk ({len(content)} chars).")
            return True
        return False

    def _process_researcher_content(self, agent: str, content: str, task_id: str) -> bool:
        """处理researcher/coder内容"""
        if content.strip():
            self.data_collector["researcher_data"].append({"agent": agent, "content": content})
            logger.debug(f"Task {task_id}: Collected {agent} content ({len(content)} chars)")
            return len(content.strip()) > 50
        else:
            logger.debug(f"Task {task_id}: Skipped empty {agent} content")
            return False

    def _process_reporter_content(self, content: str, task_id: str) -> bool:
        """处理reporter内容"""
        if content.strip():
            # Replace escaped newlines with actual newlines
            processed_content = content.replace('\n', '\n')
            self.data_collector["report_contents"].append(processed_content)
            logger.debug(f"Task {task_id}: Collected reporter content ({len(processed_content)} chars)")
            return len(processed_content.strip()) > 30
        else:
            logger.debug(f"Task {task_id}: Skipped empty reporter content")
            return False

    def _process_tool_calls_event(self, data: Dict[str, Any], task_id: str) -> bool:
        """处理tool_calls事件，获取工具名称"""
        tool_calls = data.get("tool_calls", [])
        if tool_calls:
            for tool_call in tool_calls:
                tool_info = {
                    "id": tool_call.get("id"),
                    "name": tool_call.get("name"),
                    "agent": data.get("agent"),
                    "thread_id": data.get("thread_id")
                }
                self.data_collector["tool_calls"].append(tool_info)
                logger.info(f"Task {task_id}: Collected tool call: {tool_call.get('name')}")
            return True
        return False

    def _process_tool_call_chunks_event(self, data: Dict[str, Any], task_id: str) -> bool:
        """处理tool_call_chunks事件，收集工具参数"""
        tool_call_chunks = data.get("tool_call_chunks", [])
        if tool_call_chunks:
            for chunk in tool_call_chunks:
                # 保持args的原始格式，不要强制转换为字符串
                args_value = chunk.get("args", "")
                # 如果args是字典，转换为JSON字符串；如果已经是字符串，保持不变
                if isinstance(args_value, dict):
                    args_str = json.dumps(args_value, ensure_ascii=False)
                else:
                    args_str = str(args_value) if args_value is not None else ""
                
                chunk_info = {
                    "id": chunk.get("id"),
                    "args": args_str,
                    "agent": data.get("agent"),
                    "thread_id": data.get("thread_id"),
                    "index": chunk.get("index", 0)
                }
                self.data_collector["tool_call_chunks"].append(chunk_info)
                logger.debug(f"Task {task_id}: Collected tool call chunk for {chunk.get('id')}: {len(args_str)} chars")
            return True  # 修复：返回True表示有内容添加
        return False

    def _process_tool_call_result_event(self, data: Dict[str, Any], task_id: str) -> bool:
        """处理tool_call_result事件，获取工具执行结果"""
        result_info = {
            "id": data.get("id"),
            "tool_call_id": data.get("tool_call_id"),
            "content": data.get("content", ""),
            "agent": data.get("agent"),
            "thread_id": data.get("thread_id")
        }
        self.data_collector["tool_call_results"].append(result_info)

        if result_info["content"]:
            self.data_collector["tool_calls_data"].append(result_info["content"])

        logger.info(f"Task {task_id}: Collected tool call result for {data.get('tool_call_id')}: {len(result_info['content'])} chars")
        return True

    def get_collected_data(self) -> Dict[str, Any]:
        """获取收集到的所有数据"""
        return self.data_collector.copy()

    def build_final_report(self, contents: List[str] = None) -> str:
        """
        从SSE事件构建最终report的结果
        """
        if contents is None:
            contents = self.data_collector["report_contents"]

        if not contents:
            return ""

        # Simply join the chunks without stripping to preserve newlines
        combined_content = "".join(contents)
        logger.debug(f"Built report from {len(contents)} reporter chunks, total length: {len(combined_content)}")

        return combined_content

    def build_structured_detail(self, html_path: str = None) -> Dict[str, Any]:
        """
        构建结构化的detail数据
        """
        planner_plan = self.data_collector.get("planner_plan")
        researcher_data = self.data_collector.get("researcher_data", [])
        report_contents = self.data_collector.get("report_contents", [])

        detail = {
            "thought": "",
            "plan": [],
            "execution": [],
            "final_answer": "",
            "urls": []
        }

        if planner_plan and isinstance(planner_plan, dict):
            detail["thought"] = planner_plan.get("thought", "")
            logger.info(f"Building structured detail - extracted thought: {detail['thought'][:100]}...")

            steps = planner_plan.get("steps", [])
            logger.info(f"Building structured detail - found {len(steps)} steps in planner_plan")
            for i, step in enumerate(steps, 1):
                plan_item = {
                    "step": i,
                    "title": step.get("title", ""),
                    "description": step.get("description", "")
                }
                detail["plan"].append(plan_item)
                logger.debug(f"Building structured detail - added plan step {i}: {step.get('title', '')}")

        tool_calls = self.data_collector.get("tool_calls", [])
        tool_call_chunks = self.data_collector.get("tool_call_chunks", [])
        tool_call_results = self.data_collector.get("tool_call_results", [])

        execution_items = self._build_tool_executions(tool_calls, tool_call_chunks, tool_call_results)
        detail["execution"].extend(execution_items)

        if report_contents:
            # Simply join the chunks to preserve newlines
            combined_report = "".join(report_contents)
            detail["final_answer"] = combined_report

        if html_path:
            detail["urls"].append({
                "name": "CloudBot智能体-长推理诊断报告",
                "url": html_path
            })

        return detail

    def _build_tool_executions(self, tool_calls: List[Dict[str, Any]],
                             tool_call_chunks: List[Dict[str, Any]],
                             tool_call_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """构建完整的工具执行信息"""
        executions = []
        tool_data_map = {}
        tool_call_id_to_args_chunks = {}

        # 1. 收集工具调用基本信息
        for tool_call in tool_calls:
            tool_id = tool_call.get("id") # This is the tool_call_id
            thread_id = tool_call.get("thread_id")
            if tool_id:
                tool_data_map[tool_id] = {
                    "name": tool_call.get("name"),
                    "agent": tool_call.get("agent"),
                    "args_chunks": [], # Will be populated later
                    "result": None,
                    "thread_id": thread_id
                }
                tool_call_id_to_args_chunks[tool_id] = []

        # 2. 收集参数块
        # Group chunks by their effective tool_call_id (either chunk.id or inferred from thread_id)
        # and then sort by index to ensure correct concatenation.
        target_tool_call_id = None
        for chunk in tool_call_chunks:
            chunk_tool_call_id = chunk.get("id") # This is the tool_call_id from the chunk, can be empty
            if chunk_tool_call_id:
                target_tool_call_id = chunk_tool_call_id
            if target_tool_call_id and target_tool_call_id in tool_call_id_to_args_chunks:
                tool_call_id_to_args_chunks[target_tool_call_id].append({
                    "args": chunk.get("args", ""),
                    "index": chunk.get("index", 0)
                })

        # Sort args chunks by index and combine them
        for tool_id, chunks_list in tool_call_id_to_args_chunks.items():
            chunks_list.sort(key=lambda x: x.get("index", 0))
            tool_data_map[tool_id]["args_chunks"] = [c["args"] for c in chunks_list]


        # 3. 收集结果
        for result in tool_call_results:
            tool_call_id = result.get("tool_call_id")
            if tool_call_id:
                # Find the corresponding tool_data in tool_data_map
                # The tool_call_id from result should directly match the tool_id in tool_data_map
                if tool_call_id in tool_data_map:
                    tool_data_map[tool_call_id]["result"] = result.get("content", "")
                else:
                    # Handle cases where tool_call_id from result might not directly match
                    # This could happen if tool_call_id in result is a substring or has a prefix
                    # The original code had a loop for this, let's keep it for robustness
                    found = False
                    for tool_id_key, tool_data in tool_data_map.items():
                        if tool_call_id.endswith(tool_id_key) or tool_id_key in tool_call_id:
                            tool_data["result"] = result.get("content", "")
                            found = True
                            break
                    if not found:
                        logger.warning(f"Tool call result for {tool_call_id} could not be mapped to any tool_call.")
        # 4. 构建执行信息
        for tool_id, tool_data in tool_data_map.items():
            execution_info = self._build_single_tool_execution(tool_id, tool_data)
            # 过滤掉 tool_name 为 "handoff_to_planner" 的执行记录（支持大小写不敏感和空格处理）
            tool_name = execution_info.get("tool_name", "").strip().lower()
            if tool_name != "handoff_to_planner":
                executions.append(execution_info)
        return executions

    def _build_single_tool_execution(self, tool_id: str, tool_data: Dict[str, Any]) -> Dict[str, Any]:
        """构建单个工具执行信息"""
        tool_name = tool_data.get("name", "unknown_tool")
        args_chunks = tool_data.get("args_chunks", [])
        result_content = tool_data.get("result", "")

        combined_args = "".join(args_chunks)
        parameters = self._parse_tool_arguments(combined_args)
        parsed_result = self._parse_tool_result_content(result_content)
        execution_time = None
        task_description = self._generate_tool_task_description(tool_name, parameters, parsed_result)

        return {
            "task_name": task_description,
            "tool_name": tool_name,
            "parameters": parameters,
            "result": {
                "status": "success" if parsed_result else "error",
                "output": parsed_result or result_content,
                "summary": self._generate_tool_execution_summary(tool_name, parameters, parsed_result)
            },
            "execution_time": 0.0 if execution_time is None else round(execution_time, 2)
        }

    def _parse_tool_arguments(self, args_string: str) -> Dict[str, Any]:
        """解析工具参数"""
        if not args_string or not args_string.strip():
            return {}
        try:
            repaired_json = repair_json_output(args_string)
            return json.loads(repaired_json)
        except json.JSONDecodeError:
            return {}

    def _parse_tool_result_content(self, content: str) -> Any:
        """解析工具结果内容"""
        if not content or not content.strip():
            return None
        try:
            return json.loads(content)
        except json.JSONDecodeError:
            return content

    def _generate_tool_task_description(self, tool_name: str, parameters: Dict[str, Any], result: Any) -> str:
        """生成工具任务描述"""
        base_desc = f"调用{tool_name}工具"
        param_parts = []
        if isinstance(parameters, dict):
            if "instance_id" in parameters:
                param_parts.append(f"实例{parameters['instance_id']}")
            if "instanceId" in parameters:
                param_parts.append(f"实例{parameters['instanceId']}")
            if "time_range" in parameters:
                param_parts.append(f"时间范围{parameters['time_range']}")

        if isinstance(result, list):
            param_parts.append(f"返回{len(result)}条记录")
        elif isinstance(result, dict):
            param_parts.append("返回结构化数据")

        if param_parts:
            return f"{base_desc}({', '.join(param_parts)})"
        else:
            return base_desc

    def _generate_tool_execution_summary(self, tool_name: str, parameters: Dict[str, Any], result: Any) -> str:
        """生成工具执行摘要"""
        summary_parts = [f"执行{tool_name}"]
        if isinstance(parameters, dict):
            if "instance_id" in parameters or "instanceId" in parameters:
                instance_id = parameters.get("instance_id") or parameters.get("instanceId")
                summary_parts.append(f"实例ID: {instance_id}")

        if isinstance(result, list):
            summary_parts.append(f"成功返回{len(result)}条记录")
        elif isinstance(result, dict):
            summary_parts.append("成功返回结构化数据")
        elif result:
            summary_parts.append("执行成功")
        else:
            summary_parts.append("执行完成")

        return "，".join(summary_parts)