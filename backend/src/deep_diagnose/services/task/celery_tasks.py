"""
Celery 任务定义模块

定义异步执行的 Celery 任务，包括诊断任务执行等。
"""

import asyncio
import logging
from typing import Tuple

from deep_diagnose.services.task.celery_base import celery
from deep_diagnose.services.task.task_executor import TaskExecutor
from deep_diagnose.common.config import get_config
from deep_diagnose.common.models.task_models import TaskStatus

logger = logging.getLogger(__name__)


@celery.task(queue=get_config().app_queue,name="execute_diagnosis_task")
def execute_diagnosis_task(task_id: str, agent: str, question: str) -> Tuple[str, str]:
    """
    异步执行诊断任务的 Celery 任务
    
    Args:
        task_id: 任务ID
        agent: 智能体名称
        question: 问题描述
        
    Returns:
        tuple: (complete_sse_data, report_markdown)
    """
    try:
        logger.info(f"Starting Celery task execution for {task_id}, agent: {agent}")
        
        # 获取或创建事件循环，但不强制设置新的事件循环
        try:
            # 尝试获取当前线程的事件循环
            loop = asyncio.get_running_loop()
            logger.debug(f"Using existing running loop for task {task_id}")
        except RuntimeError:
            try:
                # 如果没有运行的循环，尝试获取当前线程的事件循环
                loop = asyncio.get_event_loop()
                logger.debug(f"Using existing event loop for task {task_id}")
            except RuntimeError:
                # 如果完全没有事件循环，创建新的
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                logger.debug(f"Created new event loop for task {task_id}")
        
        # 在每个任务执行前重新初始化数据库连接，确保连接绑定到正确的事件循环
        async def _execute_with_fresh_db():
            from deep_diagnose.data.database import db_manager
            
            # 关闭可能存在的旧连接
            try:
                await db_manager.close_database()
            except Exception as e:
                logger.debug(f"No existing database connections to close: {e}")
            
            # 重新初始化数据库连接到当前事件循环
            await db_manager.init_database()
            
            # 创建任务执行器实例
            executor = TaskExecutor()
            
            try:
                # 执行任务
                result = await executor.execute_task(task_id, agent.strip(), question.strip())
                return result
            finally:
                # 任务完成后保持连接，让 worker 管理连接生命周期
                pass
        
        # 运行异步任务
        if loop.is_running():
            # 如果循环已经在运行，使用 asyncio.create_task
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, _execute_with_fresh_db())
                result = future.result()
        else:
            # 如果循环没有运行，直接运行
            result = loop.run_until_complete(_execute_with_fresh_db())
        
        logger.info(f"Celery task {task_id} completed successfully")
        return result
        
    except Exception as e:
        logger.error(f"Celery task {task_id} failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        # 更新任务状态为失败
        try:
            async def _update_failure():
                from deep_diagnose.data.database import db_manager
                
                # 确保数据库连接可用
                try:
                    if not db_manager.is_initialized:
                        await db_manager.init_database()
                except Exception as db_error:
                    logger.error(f"Failed to initialize database for error update: {db_error}")
                
                # 创建任务执行器实例
                executor = TaskExecutor()
                await executor._update_task(task_id, status=TaskStatus.FAILURE, error=str(e))
            
            # 获取事件循环并更新状态
            try:
                loop = asyncio.get_running_loop()
            except RuntimeError:
                try:
                    loop = asyncio.get_event_loop()
                except RuntimeError:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
            
            if loop.is_running():
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, _update_failure())
                    future.result()
            else:
                loop.run_until_complete(_update_failure())
                
        except Exception as update_error:
            logger.error(f"Failed to update task status for {task_id}: {update_error}")
        
        # 重新抛出异常，让 Celery 知道任务失败
        raise


@celery.task(queue=get_config().app_queue,name="cancel_diagnosis_task")
def cancel_diagnosis_task(task_id: str) -> bool:
    """
    取消诊断任务的 Celery 任务
    
    Args:
        task_id: 任务ID
        
    Returns:
        bool: 是否成功取消
    """
    try:
        logger.info(f"Cancelling Celery task {task_id}")
        
        # 获取或创建事件循环
        try:
            loop = asyncio.get_running_loop()
            logger.debug(f"Using existing running loop for cancel task {task_id}")
        except RuntimeError:
            try:
                loop = asyncio.get_event_loop()
                logger.debug(f"Using existing event loop for cancel task {task_id}")
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                logger.debug(f"Created new event loop for cancel task {task_id}")
        
        # 在独立的数据库连接中执行取消操作
        async def _cancel_with_fresh_db():
            from deep_diagnose.data.database import db_manager
            
            # 确保数据库连接可用
            try:
                if not db_manager.is_initialized:
                    await db_manager.init_database()
            except Exception as db_error:
                logger.error(f"Failed to initialize database for cancel: {db_error}")
            
            # 创建任务执行器实例
            executor = TaskExecutor()
            
            # 取消任务
            result = await executor.cancel_task(task_id)
            return result
        
        # 运行异步任务
        if loop.is_running():
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, _cancel_with_fresh_db())
                result = future.result()
        else:
            result = loop.run_until_complete(_cancel_with_fresh_db())
        
        logger.info(f"Celery task {task_id} cancelled: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Failed to cancel Celery task {task_id}: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False