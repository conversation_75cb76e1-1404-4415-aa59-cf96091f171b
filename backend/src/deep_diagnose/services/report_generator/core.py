"""
报告生成器核心接口

这个模块定义了报告生成的核心接口，任何人都能快速理解：
1. DocumentGenerator - 文档生成器接口
2. GenerationResult - 统一的生成结果
3. DocumentFormat - 支持的文档格式
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Optional
from enum import Enum


class DocumentFormat(Enum):
    """支持的文档格式"""
    HTML = "html"
    PDF = "pdf"


@dataclass
class GenerationResult:
    """统一的文档生成结果"""
    success: bool                           # 是否成功
    file_path: Optional[str] = None        # 本地文件路径
    file_size: Optional[int] = None        # 文件大小（字节）
    oss_url: Optional[str] = None          # OSS访问链接
    error_message: Optional[str] = None    # 错误信息
    format: Optional[DocumentFormat] = None # 文档格式


class DocumentGenerator(ABC):
    """
    文档生成器接口
    
    任何文档生成器都必须实现这个接口，提供两个核心功能：
    1. 生成完整诊断报告
    2. 生成简单诊断报告
    """
    
    @abstractmethod
    def generate_full_report(
        self, 
        task_id: str, 
        agent: str, 
        question: str,
        result: str, 
        detail: str,
        submitted_at: str, 
        completed_at: Optional[str] = None
    ) -> GenerationResult:
        """
        生成完整的诊断报告
        
        Args:
            task_id: 任务ID
            agent: 智能体名称
            question: 问题描述
            result: 诊断结果
            detail: 详细过程
            submitted_at: 提交时间
            completed_at: 完成时间
            
        Returns:
            生成结果
        """
        pass
    
    @abstractmethod
    def generate_simple_report(self, task_id: str, content: str) -> GenerationResult:
        """
        生成简单诊断报告
        
        Args:
            task_id: 任务ID
            content: 报告内容（Markdown格式）
            
        Returns:
            生成结果
        """
        pass
    
    @abstractmethod
    def cleanup_old_files(self, max_age_hours: int = 24) -> None:
        """
        清理旧文件
        
        Args:
            max_age_hours: 文件最大保留时间（小时）
        """
        pass
    
    @property
    @abstractmethod
    def format(self) -> DocumentFormat:
        """获取文档格式"""
        pass