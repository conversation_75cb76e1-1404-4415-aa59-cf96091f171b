"""
内容处理器

负责将Markdown内容转换为HTML或PDF元素。
代码简洁，职责单一，易于理解。
"""

import html
import re
from abc import ABC, abstractmethod
from typing import List, Any, Tuple


class ContentProcessor(ABC):
    """内容处理器基类"""

    @abstractmethod
    def process_content(self, content: str) -> Any:
        """
        处理Markdown内容

        Args:
            content: Markdown内容

        Returns:
            处理后的内容（HTML字符串或PDF元素列表）
        """
        pass

    def _parse_markdown_lines(self, content: str) -> List[Tuple[str, str, int]]:
        """
        解析Markdown行

        Returns:
            List of (line_type, content, indent_level)
        """
        lines = content.split('\n')
        parsed_lines = []

        for line in lines:
            stripped = line.strip()
            indent_level = (len(line) - len(line.lstrip())) // 2

            if not stripped:
                parsed_lines.append(("empty", "", 0))
            elif stripped.startswith('```'):
                if stripped == '```':
                    parsed_lines.append(("code_block_end", "", 0))
                else:
                    parsed_lines.append(("code_block_start", stripped[3:], 0))
            elif stripped.startswith('# '):
                parsed_lines.append(("heading_1", stripped[2:], 0))
            elif stripped.startswith('## '):
                parsed_lines.append(("heading_2", stripped[3:], 0))
            elif stripped.startswith('### '):
                parsed_lines.append(("heading_3", stripped[4:], 0))
            elif stripped.startswith('#### '):
                parsed_lines.append(("heading_4", stripped[5:], 0))
            elif stripped.startswith('|'):
                parsed_lines.append(("table_row", stripped, 0))
            elif stripped.startswith('- ') or stripped.startswith('* '):
                parsed_lines.append(("bullet_list", stripped[2:], indent_level))
            elif re.match(r'^\d+\. ', stripped):
                content_match = re.match(r'^\d+\. (.+)', stripped)
                if content_match:
                    parsed_lines.append(("numbered_list", content_match.group(1), indent_level))
            elif stripped.startswith('> '):
                parsed_lines.append(("quote", stripped[2:], 0))
            elif stripped == '---':
                parsed_lines.append(("separator", "", 0))
            else:
                parsed_lines.append(("paragraph", stripped, indent_level))

        return parsed_lines

    def _process_inline_markdown(self, text: str) -> str:
        """处理行内Markdown格式"""
        # 行内代码
        text = re.sub(r'`([^`]+)`', self._format_inline_code, text)
        # 粗体
        text = re.sub(r'\*\*([^*]+)\*\*', self._format_bold, text)
        # 斜体
        text = re.sub(r'\*([^*]+)\*', self._format_italic, text)
        # 链接
        text = re.sub(r'\[([^\]]+)\]\(([^)]+)\)', self._format_link, text)

        return text

    def _format_inline_code(self, match) -> str:
        """格式化行内代码 - 子类实现"""
        return match.group(0)

    def _format_bold(self, match) -> str:
        """格式化粗体 - 子类实现"""
        return match.group(0)

    def _format_italic(self, match) -> str:
        """格式化斜体 - 子类实现"""
        return match.group(0)

    def _format_link(self, match) -> str:
        """格式化链接 - 子类实现"""
        return match.group(0)

    def _parse_table(self, table_lines: List[str]) -> Tuple[List[str], List[List[str]]]:
        """解析表格"""
        if not table_lines:
            return [], []

        headers = []
        rows = []

        for i, line in enumerate(table_lines):
            cells = [cell.strip() for cell in line.split('|')[1:-1]]

            if i == 0:
                headers = cells
            elif i == 1 and all(cell.strip().replace('-', '').replace(':', '') == '' for cell in cells):
                continue  # 跳过分隔行
            else:
                rows.append(cells)

        return headers, rows


class HTMLContentProcessor(ContentProcessor):
    """HTML内容处理器"""

    def process_content(self, content: str) -> str:
        """将Markdown转换为HTML"""
        if not content:
            return "<p>暂无内容</p>"

        parsed_lines = self._parse_markdown_lines(content)
        html_parts = []

        # 处理状态
        in_table = False
        table_rows = []
        in_code_block = False
        code_lines = []
        in_bullet_list = False
        in_numbered_list = False
        current_list_items = []
        current_list_type = None

        def _close_current_list():
            """关闭当前列表"""
            nonlocal in_bullet_list, in_numbered_list, current_list_items, current_list_type
            if current_list_items:
                if current_list_type == "bullet":
                    html_parts.append("<ul>")
                    for item in current_list_items:
                        if isinstance(item, dict):
                            # 嵌套列表项
                            html_parts.append(f"<li>{item['content']}")
                            if item['sub_items']:
                                for sub_item in item['sub_items']:
                                    # 检查是否是HTML表格或其他HTML内容
                                    if sub_item.strip().startswith('<table'):
                                        # 表格直接添加，不包装在<li>中
                                        html_parts.append(sub_item)
                                    else:
                                        html_parts.append(f"<ul><li>{sub_item}</li></ul>")
                            html_parts.append("</li>")
                        else:
                            html_parts.append(f"<li>{item}</li>")
                    html_parts.append("</ul>")
                elif current_list_type == "numbered":
                    html_parts.append("<ol>")
                    for item in current_list_items:
                        if isinstance(item, dict):
                            # 嵌套列表项
                            html_parts.append(f"<li>{item['content']}")
                            if item['sub_items']:
                                for sub_item in item['sub_items']:
                                    # 检查是否是HTML表格或其他HTML内容
                                    if sub_item.strip().startswith('<table'):
                                        # 表格直接添加，不包装在<li>中
                                        html_parts.append(sub_item)
                                    else:
                                        html_parts.append(f"<ul><li>{sub_item}</li></ul>")
                            html_parts.append("</li>")
                        else:
                            html_parts.append(f"<li>{item}</li>")
                    html_parts.append("</ol>")

                current_list_items = []
                current_list_type = None
                in_bullet_list = False
                in_numbered_list = False

        for line_type, line_content, indent_level in parsed_lines:
            # 处理代码块
            if line_type == "code_block_start":
                _close_current_list()  # 关闭列表
                in_code_block = True
                code_lines = []
                continue
            elif line_type == "code_block_end":
                in_code_block = False
                if code_lines:
                    code_html = html.escape('\n'.join(code_lines))
                    html_parts.append(f"<pre><code>{code_html}</code></pre>")
                code_lines = []
                continue
            elif in_code_block:
                code_lines.append(line_content)
                continue

            # 处理表格
            if line_type == "table_row":
                # 不要立即关闭列表，而是收集表格数据
                if not in_table:
                    in_table = True
                    table_rows = []
                table_rows.append(line_content)
                continue
            else:
                if in_table:
                    # 表格结束，将表格添加到当前上下文
                    table_html = self._build_table(table_rows)
                    
                    # 如果在列表中，将表格作为列表项的一部分
                    if (in_bullet_list or in_numbered_list) and current_list_items:
                        # 将表格添加到最后一个列表项的子项中
                        if isinstance(current_list_items[-1], dict):
                            current_list_items[-1]['sub_items'].append(table_html)
                        else:
                            # 如果不是字典格式，转换为字典格式
                            content = current_list_items[-1] if current_list_items else ""
                            current_list_items[-1] = {'content': content, 'sub_items': [table_html]}
                    else:
                        # 不在列表中，直接添加表格
                        html_parts.append(table_html)
                    
                    in_table = False
                    table_rows = []

            # 处理列表
            if line_type == "bullet_list":
                processed_content = self._process_inline_markdown(line_content)
                if indent_level == 0:
                    # 主列表项
                    if not in_bullet_list or current_list_type != "bullet":
                        _close_current_list()  # 关闭之前的列表
                        in_bullet_list = True
                        current_list_type = "bullet"
                    current_list_items.append({'content': processed_content, 'sub_items': []})
                else:
                    # 子列表项
                    if current_list_items and isinstance(current_list_items[-1], dict):
                        current_list_items[-1]['sub_items'].append(processed_content)
                    else:
                        # 如果没有主项，创建一个空的主项
                        current_list_items.append({'content': '', 'sub_items': [processed_content]})
                continue
            elif line_type == "numbered_list":
                processed_content = self._process_inline_markdown(line_content)
                if indent_level == 0:
                    # 主列表项
                    if not in_numbered_list or current_list_type != "numbered":
                        _close_current_list()  # 关闭之前的列表
                        in_numbered_list = True
                        current_list_type = "numbered"
                    current_list_items.append({'content': processed_content, 'sub_items': []})
                else:
                    # 子列表项
                    if current_list_items and isinstance(current_list_items[-1], dict):
                        current_list_items[-1]['sub_items'].append(processed_content)
                    else:
                        # 如果没有主项，创建一个空的主项
                        current_list_items.append({'content': '', 'sub_items': [processed_content]})
                continue
            elif line_type == "empty":
                # 空行不关闭列表，允许列表项之间有空行
                continue
            else:
                # 非列表项，关闭当前列表
                _close_current_list()

            # 处理其他元素
            if line_type == "empty":
                continue
            elif line_type.startswith("heading_"):
                level = int(line_type.split("_")[1])
                processed_content = self._process_inline_markdown(line_content)
                html_parts.append(f"<h{level}>{processed_content}</h{level}>")
            elif line_type == "paragraph":
                processed_content = self._process_inline_markdown(line_content)
                if indent_level > 0:
                    html_parts.append(f'<p class="indent">{processed_content}</p>')
                else:
                    html_parts.append(f"<p>{processed_content}</p>")
            elif line_type == "quote":
                processed_content = self._process_inline_markdown(line_content)
                html_parts.append(f"<blockquote>{processed_content}</blockquote>")
            elif line_type == "separator":
                html_parts.append("<hr>")

        # 处理最后的表格和列表
        if in_table and table_rows:
            table_html = self._build_table(table_rows)
            
            # 如果在列表中，将表格作为列表项的一部分
            if (in_bullet_list or in_numbered_list) and current_list_items:
                # 将表格添加到最后一个列表项的子项中
                if isinstance(current_list_items[-1], dict):
                    current_list_items[-1]['sub_items'].append(table_html)
                else:
                    # 如果不是字典格式，转换为字典格式
                    content = current_list_items[-1] if current_list_items else ""
                    current_list_items[-1] = {'content': content, 'sub_items': [table_html]}
            else:
                # 不在列表中，直接添加表格
                html_parts.append(table_html)

        _close_current_list()  # 关闭最后的列表

        return '\n'.join(html_parts)

    def _format_inline_code(self, match) -> str:
        code_text = match.group(1)
        return f"<code>{html.escape(code_text)}</code>"

    def _format_bold(self, match) -> str:
        bold_text = match.group(1)
        return f"<strong>{bold_text}</strong>"

    def _format_italic(self, match) -> str:
        italic_text = match.group(1)
        return f"<em>{italic_text}</em>"

    def _format_link(self, match) -> str:
        link_text = match.group(1)
        link_url = match.group(2)
        return f'<a href="{html.escape(link_url)}">{html.escape(link_text)}</a>'

    def _build_table(self, table_lines: List[str]) -> str:
        """构建HTML表格"""
        if not table_lines:
            return ""

        headers, rows = self._parse_table(table_lines)
        html_parts = ["<table>"]

        # 表头
        if headers:
            html_parts.append("<thead><tr>")
            for header in headers:
                processed_header = self._process_inline_markdown(header)
                html_parts.append(f"<th>{processed_header}</th>")
            html_parts.append("</tr></thead>")

        # 表体
        if rows:
            html_parts.append("<tbody>")
            for row in rows:
                html_parts.append("<tr>")
                for cell in row:
                    processed_cell = self._process_inline_markdown(cell)
                    html_parts.append(f"<td>{processed_cell}</td>")
                html_parts.append("</tr>")
            html_parts.append("</tbody>")

        html_parts.append("</table>")
        return '\n'.join(html_parts)


class PDFContentProcessor(ContentProcessor):
    """PDF内容处理器"""

    def __init__(self, config):
        self.config = config

    def process_content(self, content: str) -> List:
        """将Markdown转换为PDF元素列表"""
        try:
            from reportlab.platypus import Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.lib import colors
        except ImportError:
            raise ImportError("reportlab is required for PDF generation")

        if not content:
            return [Paragraph("暂无内容", getSampleStyleSheet()['Normal'])]

        elements = []
        styles = getSampleStyleSheet()

        # 自定义样式
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=12,
            textColor=colors.HexColor('#3182CE')
        )

        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=8,
            textColor=colors.HexColor('#3182CE')
        )

        parsed_lines = self._parse_markdown_lines(content)

        for line_type, line_content, indent_level in parsed_lines:
            if line_type == "empty":
                elements.append(Spacer(1, 6))
            elif line_type == "heading_1":
                elements.append(Paragraph(line_content, title_style))
            elif line_type == "heading_2":
                elements.append(Paragraph(line_content, heading_style))
            elif line_type == "heading_3":
                elements.append(Paragraph(line_content, styles['Heading3']))
            elif line_type == "paragraph":
                elements.append(Paragraph(line_content, styles['Normal']))
            elif line_type in ["bullet_list", "numbered_list"]:
                elements.append(Paragraph(f"• {line_content}", styles['Normal']))
            elif line_type == "quote":
                elements.append(Paragraph(line_content, styles['Normal']))
            elif line_type == "separator":
                elements.append(Spacer(1, 12))

        return elements