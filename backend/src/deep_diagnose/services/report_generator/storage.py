"""
存储管理器

统一的文件存储和OSS上传功能，消除重复代码。
"""

import os
import logging
import time
from pathlib import Path
from typing import Optional

from .core import GenerationResult, DocumentFormat

logger = logging.getLogger(__name__)


class StorageManager:
    """统一存储管理器"""
    
    def __init__(self, output_dir: str, enable_oss: bool = True, format_type: DocumentFormat = DocumentFormat.HTML):
        """
        初始化存储管理器
        
        Args:
            output_dir: 本地输出目录
            enable_oss: 是否启用OSS上传
            format_type: 文档格式类型
        """
        self.output_dir = output_dir
        self.enable_oss = enable_oss
        self.format_type = format_type
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        logger.info(f"Storage Manager initialized: {output_dir}, OSS: {enable_oss}, Format: {format_type.value}")
    
    def save_file(self, file_path: str, task_id: str) -> GenerationResult:
        """
        保存文件并可选上传到OSS
        
        Args:
            file_path: 本地文件路径
            task_id: 任务ID
            
        Returns:
            存储结果
        """
        try:
            if not os.path.exists(file_path):
                return GenerationResult(
                    success=False,
                    error_message=f"File not found: {file_path}",
                    format=self.format_type
                )
            
            file_size = os.path.getsize(file_path)
            oss_url = None
            
            # 如果启用OSS，尝试上传
            if self.enable_oss:
                try:
                    oss_url = self._upload_to_oss(file_path, task_id)
                    logger.info(f"Successfully uploaded {self.format_type.value} to OSS: {oss_url}")
                except Exception as e:
                    logger.warning(f"Failed to upload {self.format_type.value} to OSS: {e}")
                    # OSS上传失败不影响整体成功
            
            return GenerationResult(
                success=True,
                file_path=file_path,
                file_size=file_size,
                oss_url=oss_url,
                format=self.format_type
            )
            
        except Exception as e:
            error_msg = f"Failed to save {self.format_type.value} file: {str(e)}"
            logger.error(error_msg)
            return GenerationResult(
                success=False,
                error_message=error_msg,
                format=self.format_type
            )
    
    def _upload_to_oss(self, file_path: str, task_id: str) -> str:
        """
        上传文件到OSS
        
        Args:
            file_path: 本地文件路径
            task_id: 任务ID
            
        Returns:
            OSS签名URL
        """
        try:
            from deep_diagnose.storage.oss_client import OssClient
            from deep_diagnose.common.config import get_config
            
            # 获取OSS上传目录配置
            try:
                oss_upload_dir = get_config().infrastructure.oss.upload_dir
            except Exception:
                oss_upload_dir = f"{self.format_type.value}_reports"

            oss_client = OssClient()
            filename = os.path.basename(file_path)
            file_extension = os.path.splitext(filename)[1]
            oss_key = f"{oss_upload_dir}_{task_id}{file_extension}"
            # 上传文件
            oss_client.upload_file(file_path, oss_key)
            # 生成签名URL (30天有效期)
            signed_url = oss_client.get_file_url( oss_key, 2592000)
            logger.info(f"{self.format_type.value.upper()} report uploaded to OSS: {signed_url}")
            return signed_url
            
        except ImportError:
            logger.error("OSS client not available, skipping upload")
            raise Exception("OSS client not available")
        except Exception as e:
            logger.error(f"OSS upload failed: {e}")
            raise
    
    def cleanup_old_files(self, max_age_hours: int = 24) -> None:
        """
        清理旧文件
        
        Args:
            max_age_hours: 文件最大保留时间（小时）
        """
        try:
            current_time = time.time()
            cutoff_time = current_time - (max_age_hours * 3600)
            
            output_path = Path(self.output_dir)
            file_pattern = f"diagnostic_report_*.{self.format_type.value}"
            
            for file_path in output_path.glob(file_pattern):
                if os.path.getmtime(file_path) < cutoff_time:
                    file_path.unlink()
                    logger.info(f"Cleaned up old {self.format_type.value} file: {file_path}")
            
            logger.info(f"Cleaned up {self.format_type.value} files older than {max_age_hours} hours")
        except Exception as e:
            logger.warning(f"Failed to cleanup old {self.format_type.value} files: {e}")