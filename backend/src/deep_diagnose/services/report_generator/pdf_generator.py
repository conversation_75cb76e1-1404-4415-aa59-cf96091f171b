"""
PDF生成器

简洁的PDF报告生成器，职责单一，接口清晰。
"""

import logging
import os
import tempfile
from datetime import datetime
from typing import Optional

from .core import DocumentGenerator, GenerationResult, DocumentFormat
from .config import PDFConfig
from .processors import PDFContentProcessor
from .storage import StorageManager

logger = logging.getLogger(__name__)

# 检查reportlab可用性
try:
    import reportlab
    from reportlab.lib import colors
    from reportlab.platypus import PageTemplate, Frame, BaseDocTemplate
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False


class ProfessionalDocTemplate(BaseDocTemplate):
    """专业PDF文档模板，包含页眉页脚"""
    
    def __init__(self, filename, **kwargs):
        self.title = kwargs.pop('title', 'CloudBot智能体深度诊断报告')
        BaseDocTemplate.__init__(self, filename, **kwargs)
        
        # 定义页面框架
        frame = Frame(
            self.leftMargin,
            self.bottomMargin,
            self.width,
            self.height,
            id='normal',
            leftPadding=6,
            bottomPadding=6,
            rightPadding=6,
            topPadding=6
        )
        
        # 创建页面模板
        template = PageTemplate(id='main', frames=frame, onPage=self._add_page_decorations)
        self.addPageTemplates([template])
    
    def _add_page_decorations(self, canvas, doc):
        """添加页眉页脚装饰"""
        canvas.saveState()
        
        # 页眉区域
        page_width, page_height = canvas._pagesize
        header_y = page_height - doc.topMargin + 20
        
        # 页眉标题
        canvas.setFont('Helvetica-Bold', 12)
        canvas.setFillColor(colors.HexColor('#3182CE'))
        title_text = "CloudBot智能体-深度诊断报告"
        canvas.drawString(doc.leftMargin, header_y, title_text)
        
        # 页眉分割线
        canvas.setStrokeColor(colors.HexColor('#BDC3C7'))
        canvas.setLineWidth(0.5)
        canvas.line(doc.leftMargin, header_y - 5, doc.width + doc.leftMargin, header_y - 5)
        
        # 页脚区域
        footer_y = doc.bottomMargin - 15
        
        # 页脚分割线
        canvas.line(doc.leftMargin, footer_y + 18, doc.width + doc.leftMargin, footer_y + 18)
        
        # 页脚内容
        canvas.setFont('Helvetica', 8)
        canvas.setFillColor(colors.HexColor('#7F8C8D'))
        
        # 页码
        page_num = canvas.getPageNumber()
        canvas.drawRightString(doc.width + doc.leftMargin, footer_y + 5, f"第 {page_num} 页")
        
        # 生成时间
        now = datetime.now().strftime("%Y-%m-%d %H:%M")
        canvas.drawString(doc.leftMargin, footer_y + 5, f"生成时间: {now}")
        
        canvas.restoreState()


class PDFGenerator(DocumentGenerator):
    """
    PDF报告生成器
    
    功能：
    1. 将Markdown内容转换为PDF报告
    2. 支持本地保存和OSS上传
    3. 自动清理旧文件
    """
    
    def __init__(self, output_dir: Optional[str] = None, enable_oss: bool = True):
        """
        初始化PDF生成器
        
        Args:
            output_dir: 输出目录，如果为None则使用临时目录
            enable_oss: 是否启用OSS上传
        """
        if not REPORTLAB_AVAILABLE:
            raise ImportError("reportlab is required for PDF generation. Install with: pip install reportlab")
        
        self.output_dir = output_dir or tempfile.gettempdir()
        self.enable_oss = enable_oss
        
        # 初始化组件
        self.config = PDFConfig(self.output_dir, enable_oss)
        self.processor = PDFContentProcessor(self.config)
        self.storage = StorageManager(self.output_dir, enable_oss, DocumentFormat.PDF)
        
        logger.info(f"PDF Generator initialized: {self.output_dir}, OSS: {enable_oss}")
    
    @property
    def format(self) -> DocumentFormat:
        """获取文档格式"""
        return DocumentFormat.PDF
    
    def generate_full_report(
        self, 
        task_id: str, 
        agent: str, 
        question: str,
        result: str, 
        detail: str,
        submitted_at: str, 
        completed_at: Optional[str] = None
    ) -> GenerationResult:
        """
        生成完整的诊断报告
        
        Args:
            task_id: 任务ID
            agent: 智能体名称
            question: 问题描述
            result: 诊断结果
            detail: 详细过程
            submitted_at: 提交时间
            completed_at: 完成时间
            
        Returns:
            生成结果
        """
        try:
            # 构建完整报告内容
            content = self._build_full_report_content(
                task_id, agent, question, result, detail, submitted_at, completed_at
            )
            
            # 生成PDF文档
            return self._generate_pdf_document(task_id, content)
            
        except Exception as e:
            error_msg = f"Failed to generate full PDF report for task {task_id}: {str(e)}"
            logger.error(error_msg)
            return GenerationResult(
                success=False,
                error_message=error_msg,
                format=self.format
            )
    
    def generate_simple_report(self, task_id: str, content: str) -> GenerationResult:
        """
        生成简单诊断报告
        
        Args:
            task_id: 任务ID
            content: 报告内容（Markdown格式）
            
        Returns:
            生成结果
        """
        try:
            return self._generate_pdf_document(task_id, content)
            
        except Exception as e:
            error_msg = f"Failed to generate simple PDF report for task {task_id}: {str(e)}"
            logger.error(error_msg)
            return GenerationResult(
                success=False,
                error_message=error_msg,
                format=self.format
            )
    
    def cleanup_old_files(self, max_age_hours: int = 24) -> None:
        """
        清理旧文件
        
        Args:
            max_age_hours: 文件最大保留时间（小时）
        """
        self.storage.cleanup_old_files(max_age_hours)
    
    def _build_full_report_content(
        self, 
        task_id: str, 
        agent: str, 
        question: str,
        result: str, 
        detail: str,
        submitted_at: str, 
        completed_at: Optional[str] = None
    ) -> str:
        """构建完整报告的Markdown内容"""
        completed_time = completed_at or datetime.now().isoformat()
        
        return f"""# CloudBot智能体深度诊断报告

## 问题描述

{question}

## 诊断结果

{result}

## 详细过程

{detail}

---

报告生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
"""
    
    def _generate_pdf_document(self, task_id: str, content: str) -> GenerationResult:
        """生成PDF文档"""
        try:
            # 生成文件路径
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"diagnostic_report_{task_id}_{timestamp}.pdf"
            file_path = os.path.join(self.output_dir, filename)
            
            # 创建PDF文档
            doc_config = self.config.get_document_config()
            doc = ProfessionalDocTemplate(
                file_path,
                title="CloudBot智能体深度诊断报告",
                **doc_config
            )
            
            # 处理内容
            elements = self.processor.process_content(content)
            
            # 构建PDF
            doc.build(elements)
            
            logger.info(f"PDF document generated: {file_path}")
            
            # 保存文件（包含OSS上传）
            return self.storage.save_file(file_path, task_id)
            
        except Exception as e:
            error_msg = f"Failed to generate PDF document: {str(e)}"
            logger.error(error_msg)
            return GenerationResult(
                success=False,
                error_message=error_msg,
                format=self.format
            )


def is_pdf_generation_available() -> bool:
    """检查PDF生成功能是否可用"""
    return REPORTLAB_AVAILABLE


def create_pdf_generator(output_dir: Optional[str] = None, enable_oss: bool = True) -> PDFGenerator:
    """创建PDF生成器实例"""
    if not REPORTLAB_AVAILABLE:
        raise ImportError("reportlab is required for PDF generation. Install with: pip install reportlab")
    return PDFGenerator(output_dir, enable_oss)
