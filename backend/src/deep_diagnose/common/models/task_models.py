"""
任务相关的数据模型

独立的任务模型定义，避免循环导入问题。
"""

from typing import Optional
from enum import Enum
from pydantic import BaseModel, Field


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"
    CANCEL = "CANCEL"


class TaskRequest(BaseModel):
    """提交诊断任务的请求模型"""
    agent: str = Field(..., description="指定处理该问题的智能体或工作流名称")
    question: str = Field(..., description="描述需要执行的具体任务或问题")


class TaskResponse(BaseModel):
    """提交任务后的响应模型"""
    task_id: str = Field(..., description="任务的唯一标识符")


class TaskData(BaseModel):
    """任务数据模型"""
    result: Optional[str] = Field(None, description="最终结果报告（Markdown格式）")
    detail: Optional[str] = Field(None, description="过程明细（Markdown格式）")
    error: Optional[str] = Field(None, description="错误信息")


class TaskStatusResponse(BaseModel):
    """查询任务状态的响应模型"""
    task_id: str = Field(..., description="任务的唯一标识符")
    status: TaskStatus = Field(..., description="任务当前状态")
    submitted_at: str = Field(..., description="任务提交时间")
    completed_at: Optional[str] = Field(None, description="任务完成时间")
    data: Optional[TaskData] = Field(None, description="任务数据")