"""
Environment management for configuration.
"""

import os
from enum import Enum
from typing import List


class Environment(Enum):
    """Application environment types."""
    DEVELOPMENT = "development"
    DAILY = "daily"
    PRE = "pre"
    PRE1 = "pre1"
    PROD = "prod"


# Production environments
PRODUCTION_ENVIRONMENTS: List[str] = [
    Environment.PRE.value,
    Environment.PRE1.value,
    Environment.PROD.value
]


def get_environment() -> Environment:
    """
    Get current application environment.
    
    Returns:
        Environment: Current environment enum value
    """
    env_name = os.getenv('APP_ENV', Environment.DAILY.value).lower()
    
    # Try to match with enum values
    for env in Environment:
        if env.value == env_name:
            return env
    
    # Default to development if not found
    return Environment.DEVELOPMENT


def is_production_environment(env: Environment = None) -> bool:
    """
    Check if current or given environment is production.
    
    Args:
        env: Environment to check. If None, uses current environment.
        
    Returns:
        bool: True if production environment
    """
    if env is None:
        env = get_environment()
    
    return env.value in PRODUCTION_ENVIRONMENTS


def get_config_file_name(env: Environment = None) -> str:
    """
    Get configuration file name for environment.
    
    Args:
        env: Environment to get config for. If None, uses current environment.
        
    Returns:
        str: Configuration file name
    """
    if env is None:
        env = get_environment()
    
    if is_production_environment(env):
        return "config_prod.yaml"
    else:
        return "config_daily.yaml"
