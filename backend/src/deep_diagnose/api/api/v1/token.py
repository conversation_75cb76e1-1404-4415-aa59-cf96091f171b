"""token相关的API路由处理模块

本模块包含与验证用户token的API路由处理函数。
支持YAML配置和数据库两种认证方式，通过配置开关控制。
"""

import logging
from fastapi import APIRouter, status, HTTPException
from fastapi.responses import JSONResponse

from deep_diagnose.api.models.request_model import TokenRequestModel
from deep_diagnose.services.security.token_service import token_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/api/token")
async def token(token_request: TokenRequestModel):
    """
    用户认证并生成JWT token
    
    支持两种认证方式：
    1. YAML配置认证（默认）
    2. 数据库认证（通过配置开关启用）
    
    Args:
        token_request: 包含用户名、密码和token有效期的请求模型
        
    Returns:
        JSONResponse: 包含access_token的响应或错误信息
    """
    try:
        access_key = token_request.access_key
        secret_key = token_request.secret_key
        token_lifetime_minutes = token_request.token_lifetime_minutes
        
        logger.info(f"Token request received for user: {access_key}")
        
        # 使用token service进行认证和token生成
        access_token = await token_service.authenticate_and_generate_token(
            username=access_key,
            password=secret_key,
            token_lifetime_minutes=token_lifetime_minutes
        )
        
        if not access_token:
            logger.warning(f"Authentication failed for user: {access_key}")
            return JSONResponse(
                content={"msg": "Bad access_key or secret_key"},
                status_code=status.HTTP_401_UNAUTHORIZED,
            )
        
        logger.info(f"Token generated successfully for user: {access_key}")
        return JSONResponse(content={"access_token": access_token})
        
    except Exception as e:
        logger.error(f"Error processing token request: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during token generation"
        )


@router.get("/api/token/auth-info")
async def get_auth_info():
    """
    获取当前认证配置信息
    
    Returns:
        JSONResponse: 当前认证配置信息
    """
    try:
        auth_info = token_service.get_authentication_info()
        return JSONResponse(content=auth_info)
    except Exception as e:
        logger.error(f"Error getting auth info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error getting auth info"
        )


