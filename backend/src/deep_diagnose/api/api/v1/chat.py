"""聊天相关的API路由处理模块

本模块包含与聊天功能相关的API路由处理函数。
业务逻辑已迁移到 chat_service.py 中，此模块专注于HTTP请求处理。
"""

import logging
from typing import Optional
from uuid import uuid4

from fastapi import APIRouter, Depends, Query, HTTPException
from fastapi.responses import StreamingResponse

from deep_diagnose.security.auth.user_context import get_current_user
from deep_diagnose.api.models.chat_request import ChatRequest
from deep_diagnose.api.models.model import UserModel
from deep_diagnose.services.chat.chat_service import create_chat_service
from deep_diagnose.domain.chat.repository import chat_repository
from deep_diagnose.domain.chat.schemas import (
    ChatSessionHistoryResponse, 
    ChatSessionHistory,
    ChatMessagesResponse,
    ChatMessageHistory
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/chat", tags=["chat"])


@router.post("/stream")
async def chat_stream(request: ChatRequest, user: UserModel = Depends(get_current_user)):
    """处理聊天流请求
    
    要求携带有效 JWT Token。业务逻辑委托给 ChatService 处理。
    
    Args:
        request: 聊天请求对象
        user: 当前用户信息

    Returns:
        流式响应对象
    """
    try:
        # 创建聊天服务实例
        chat_service = create_chat_service()

        # 从messages中提取问题
        messages = request.model_dump()["messages"]
        question = ""
        if messages:
            # 找到最后一个用户消息作为问题
            for msg in reversed(messages):
                if msg.get("role") == "user":
                    question = msg.get("content", "")
                    break
        
        # 调用聊天服务生成流式响应
        return StreamingResponse(
            chat_service.chat(
                question=question,
                user_id=user.user_id if hasattr(user, 'user_id') else "anonymous",
                session_id=None,
                messages=messages
            ),
            media_type="text/event-stream",
        )
    
    except Exception as e:
        logger.error(f"Error in chat_stream endpoint: {e}")
        # 重新抛出异常，让FastAPI的异常处理器处理
        raise


@router.get("/sessions", response_model=ChatSessionHistoryResponse)
async def get_chat_sessions(
    user: UserModel = Depends(get_current_user),
    user_id: Optional[str] = Query(None, description="用户ID，如果不提供则使用当前用户"),
    start_time: Optional[str] = Query(None, description="开始时间 (ISO格式)"),
    end_time: Optional[str] = Query(None, description="结束时间 (ISO格式)"),
    topn: int = Query(20, ge=1, le=100, description="返回数量限制")
):
    """获取用户的聊天会话历史记录
    
    Args:
        user: 当前用户信息
        user_id: 查询的用户ID，如果不提供则使用当前用户ID
        start_time: 开始时间过滤
        end_time: 结束时间过滤
        topn: 返回的会话数量限制
        
    Returns:
        会话历史记录列表
    """
    try:
        # 如果没有提供user_id，使用当前用户的ID
        target_user_id = user_id or (user.user_id if hasattr(user, 'user_id') else "anonymous")
        
        # 从repository获取会话列表
        sessions = await chat_repository.get_user_sessions(
            user_id=target_user_id,
            start_time=start_time,
            end_time=end_time,
            limit=topn
        )
        
        # 转换为响应格式
        session_histories = []
        for session in sessions:
            session_history = ChatSessionHistory(
                session_id=session.session_id,
                title=session.title or "未命名会话",
                time=session.gmt_create
            )
            session_histories.append(session_history)
        
        logger.info(f"Retrieved {len(session_histories)} sessions for user {target_user_id}")
        
        return ChatSessionHistoryResponse(
            sessions=session_histories,
            total=len(session_histories)
        )
        
    except Exception as e:
        logger.error(f"Error in get_chat_sessions endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"获取会话历史失败: {str(e)}")


@router.get("/sessions/{session_id}", response_model=ChatMessagesResponse)
async def get_session_messages(
    session_id: str,
    user: UserModel = Depends(get_current_user)
):
    """获取指定会话的消息历史记录
    
    Args:
        session_id: 会话ID
        user: 当前用户信息
        
    Returns:
        会话的消息列表
    """
    try:
        # 首先验证会话是否存在
        session = await chat_repository.get_session_by_id(session_id)
        if not session:
            raise HTTPException(status_code=404, detail=f"会话 {session_id} 不存在")
        
        # 获取会话的所有消息
        messages = await chat_repository.get_session_messages(session_id)
        
        # 转换为响应格式
        message_histories = []
        for message in messages:
            message_history = ChatMessageHistory(
                id=message.id,
                message=message.message,
                message_type=message.message_type,
                gmt_create=message.gmt_create,
                status=message.status
            )
            message_histories.append(message_history)
        
        logger.info(f"Retrieved {len(message_histories)} messages for session {session_id}")
        
        return ChatMessagesResponse(
            session_id=session_id,
            messages=message_histories,
            total=len(message_histories)
        )
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"Error in get_session_messages endpoint: {e}")
        raise HTTPException(status_code=500, detail=f"获取会话消息失败: {str(e)}")
