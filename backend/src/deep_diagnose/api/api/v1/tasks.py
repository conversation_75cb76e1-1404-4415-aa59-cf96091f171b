"""
任务相关的API路由处理模块

本模块包含与异步诊断任务相关的API路由处理函数。
"""

import logging

from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.responses import JSONResponse

from deep_diagnose.api.models.model import UserModel
from deep_diagnose.common.models.task_models import TaskRequest, TaskResponse, TaskStatusResponse
from deep_diagnose.security.auth.user_context import get_current_user
from deep_diagnose.services.task.task_service import task_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1", tags=["tasks"])


@router.post("/tasks", response_model=TaskResponse, status_code=status.HTTP_202_ACCEPTED)
async def create_task(
    request: TaskRequest,
    user: UserModel = Depends(get_current_user)
) -> TaskResponse:
    """
    提交诊断任务
    
    提交一个异步诊断任务。服务器接受请求后，立即返回一个唯一的任务ID，并在后台开始处理。
    
    Args:
        request: 任务请求对象
        user: 当前用户信息
        
    Returns:
        包含任务ID的响应对象
        
    Raises:
        HTTPException: 当请求参数无效或任务创建失败时
    """
    try:
        # 验证agent参数
        if not request.agent or not request.agent.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="agent字段不能为空"
            )
        
        # 验证question参数
        if not request.question or not request.question.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="question字段不能为空"
            )
        
        # 创建任务
        task_id = await task_service.create_task(
            agent=request.agent.strip(),
            question=request.question.strip(),
            user_id=user.user_id
        )
        
        logger.info(f"Created and started task {task_id} for user {user.user_id}")
        
        return TaskResponse(task_id=task_id)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create task: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="任务创建失败，请稍后重试"
        )


@router.get("/tasks/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(
    task_id: str,
    user: UserModel = Depends(get_current_user)
) -> TaskStatusResponse:
    """
    查询任务状态与结果
    
    根据任务ID查询任务的最新状态、过程明细和最终报告。
    
    Args:
        task_id: 任务的唯一标识符
        user: 当前用户信息
        
    Returns:
        任务状态响应对象
        
    Raises:
        HTTPException: 当任务不存在时
    """
    try:
        # 验证task_id参数
        if not task_id or not task_id.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="task_id不能为空"
            )
        
        # 获取任务信息
        task_info = await task_service.get_task(task_id.strip())
        
        if not task_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"任务 {task_id} 不存在或已过期"
            )
        
        logger.debug(f"Retrieved task {task_id} status: {task_info.status}")
        
        return task_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get task {task_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取任务状态失败，请稍后重试"
        )


@router.get("/tasks/{task_id}/status")
async def get_task_status_simple(
    task_id: str,
    user: UserModel = Depends(get_current_user)
) -> JSONResponse:
    """
    简化的任务状态查询接口
    
    只返回任务的基本状态信息，不包含详细数据。
    
    Args:
        task_id: 任务的唯一标识符
        user: 当前用户信息
        
    Returns:
        包含任务状态的JSON响应
    """
    try:
        task_info = await task_service.get_task(task_id.strip())
        
        if not task_info:
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={"error": f"任务 {task_id} 不存在或已过期"}
            )
        
        return JSONResponse(
            content={
                "task_id": task_info.task_id,
                "status": task_info.status.value,
                "submitted_at": task_info.submitted_at,
                "completed_at": task_info.completed_at
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to get simple task status {task_id}: {e}", exc_info=True)
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"error": "获取任务状态失败，请稍后重试"}
        )
