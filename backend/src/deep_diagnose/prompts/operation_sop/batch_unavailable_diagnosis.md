# 批量问题不可用诊断方案

## 适用场景
**对于涉及多个实例的批量不可用问题，必须严格按照以下3个步骤执行。**

## 识别规则
**如果用户问题包含以下特征，必须使用此诊断方案：**
- 提到多个实例ID（2个或以上）
- 描述"批量"、"多个"、"这些实例"等词汇
- 实例在相同时间段出现相同问题

## 诊断步骤（强制遵循）

### 步骤1：查询实例在出问题时间所在NC
**目标**: 排查故障VM是否存在物理机（NC）层面的聚集性，是否多个出现问题的VM在出故障时在同一个物理机

- **工具**: listVMHostHistory
- **目标**: 识别实例在指定时间（过去时间）所在的NC
- **要求**: 描述中必须包含完整的实例ID列表，格式如：使用listVMHostHistory工具查询实例[i-xxx, i-yyy, i-zzz]在[具体时间范围]所在的物理机（NC），分析是否存在物理机层面的聚集性故障

### 步骤2：逐一诊断实例的不可用性及根本原因
**目标**: 诊断每个实例的不可用根本原因

- **工具**: runVMUnavailableDiagnose  
- **目标**: 诊断每个实例的不可用根本原因
- **要求**: 描述中必须包含完整的实例ID列表，格式如：使用runVMUnavailableDiagnose工具诊断实例[i-xxx, i-yyy, i-zzz]在[具体时间范围]的不可用根本原因

### 步骤3：排查实例关联的高危变更记录
**目标**: 检查是否有变更导致的问题

- **工具**: listChangeRecords
- **目标**: 检查是否有变更导致的问题
- **要求**: 描述中必须包含完整的实例ID列表，格式如：使用listChangeRecords工具查询实例[i-xxx, i-yyy, i-zzz]在[具体时间范围]的高危变更记录

## 执行要求
- **批量问题强制**: 必须严格使用以上3个步骤，不得增加或减少
- **实例ID必须**: 所有步骤描述中必须包含完整的实例ID列表，不能使用"这些实例"、"12个实例"等模糊表述
- **同类合并**: 同一类型的操作必须合并到一个步骤中，一个步骤可以包含多个资源的相同操作
- **时间精确**: 必须包含具体的时间范围，格式如：2025-06-26 01:00:00至2025-06-26 04:00:00
- **工具专用**: 每个步骤只使用一个指定工具，避免复杂的工具组合调用