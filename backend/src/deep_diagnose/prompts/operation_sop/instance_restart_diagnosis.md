# 实例重启或者宕机诊断方案

## 适用场景
**对于单个实例或少量实例的重启、宕机问题诊断。**

## 诊断步骤

### 步骤1：分析物理机 (NC) 是否为根本原因
**目标**: 检查实例所在的物理服务器（NC）是否存在可能影响实例的故障或操作

#### 1.0 获取实例基本信息
- 使用`getVmBasicInfo`根据实例查询对应NCIP

#### 1.1 查询NC异常事件
- 使用 `listMonitorExceptions` 工具查询NC的异常事件，分析是否存在宕机或硬件故障

#### 1.2 查询NC运维记录
- 使用 `listOperationRecords` 工具查询NC的运维记录，分析是否有重启或维护操作

#### 1.3 查询NC变更记录
- 使用 `listChangeRecords` 工具查询NC的变更记录，分析是否有相关硬件或配置变更

### 步骤2：分析实例本身是否发生重启或宕机事件
**目标**: 检查实例层面是否有明确的重启指令、异常事件或外部通知

#### 2.1 查询实例异常事件
- 使用 `listMonitorExceptions` 工具查询实例的异常事件，分析是否有重启或宕机记录

#### 2.2 查询控制台操作记录
- 使用 `listActionTrail` 工具查询实例的控制台操作记录，分析是否有人为重启操作

#### 2.3 查询客户侧事件
- 使用 `listReportedOperationalEvents` 工具查询实例的客户侧事件，分析是否有要求重启的通知

### 步骤3：分析是否因实例内部问题导致
**目标**: 判断是否因实例操作系统内部错误或高负载导致问题

#### 3.1 分析内核崩溃
- 使用 `get_vm_coredump` 工具分析实例的内核崩溃导致重启

### 步骤4：最后兜底分析
**目标**: 获取全面的实例状态信息作为补充或在无明确发现时使用

#### 4.1 通用诊断
- 使用 `runDiagnose` 工具对实例执行通用诊断，分析报告中的可用性和性能结论

## 执行要求
- **逐步排查**: 按照物理机 → 实例 → 内部问题 → 兜底分析的顺序进行
- **具体信息**: 所有步骤描述中必须包含具体的实例ID和时间范围
- **工具专用**: 每个步骤只使用一个指定工具
- **时间精确**: 必须包含具体的时间范围，格式如：2025-06-26 01:00:00至2025-06-26 04:00:00