"""
聊天领域数据结构
"""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field


# Chat Session Schemas
class ChatSessionBase(BaseModel):
    """会话基础模型"""
    session_id: str = Field(..., max_length=64, description="会话id")
    user_id: str = Field(..., max_length=16, description="员工工号")
    title: str = Field(default="", max_length=512, description="会话标题")
    ext: str = Field(default="", max_length=64, description="扩展字段")
    instance_id: str = Field(default="", max_length=64, description="诊断实例id")


class ChatSessionCreate(ChatSessionBase):
    """创建会话模型"""
    pass


class ChatSessionUpdate(BaseModel):
    """更新会话模型"""
    session_id: Optional[str] = Field(None, max_length=64, description="会话id")
    user_id: Optional[str] = Field(None, max_length=16, description="员工工号")
    title: Optional[str] = Field(None, max_length=512, description="会话标题")
    ext: Optional[str] = Field(None, max_length=64, description="扩展字段")
    instance_id: Optional[str] = Field(None, max_length=64, description="诊断实例id")


class ChatSession(ChatSessionBase):
    """会话响应模型"""
    id: int
    gmt_create: datetime
    gmt_modified: datetime
    
    class Config:
        from_attributes = True


# Chat Message Schemas
class ChatMessageBase(BaseModel):
    """消息基础模型"""
    session_id: str = Field(..., max_length=64, description="会话id")
    message: str = Field(..., description="聊天信息")
    message_type: str = Field(default="", max_length=16, description="发送者，ai:机器人回复，human:人工回复")
    scenario: str = Field(default="", max_length=16, description="场景编码")
    source: int = Field(default=0, description="来源，0:pc端机器人会话，1:全链路定界，2:钉钉对话")
    message_id: int = Field(default=0, description="问题ID")
    status: int = Field(default=0, description="是否完成请求，0:已完成，1未完成")


class ChatMessageCreate(ChatMessageBase):
    """创建消息模型"""
    pass


class ChatMessageUpdate(BaseModel):
    """更新消息模型"""
    session_id: Optional[str] = Field(None, max_length=64, description="会话id")
    message: Optional[str] = Field(None, description="聊天信息")
    message_type: Optional[str] = Field(None, max_length=16, description="发送者，ai:机器人回复，human:人工回复")
    scenario: Optional[str] = Field(None, max_length=16, description="场景编码")
    source: Optional[int] = Field(None, description="来源，0:pc端机器人会话，1:全链路定界，2:钉钉对话")
    message_id: Optional[int] = Field(None, description="问题ID")
    status: Optional[int] = Field(None, description="是否完成请求，0:已完成，1未完成")


class ChatMessage(ChatMessageBase):
    """消息响应模型"""
    id: int
    gmt_create: datetime
    gmt_modified: datetime
    
    class Config:
        from_attributes = True


# 历史记录查询响应模型
class ChatSessionHistory(BaseModel):
    """会话历史记录响应模型"""
    session_id: str = Field(..., description="会话ID")
    title: str = Field(..., description="会话标题")
    time: datetime = Field(..., description="创建时间")
    
    class Config:
        from_attributes = True


class ChatSessionHistoryResponse(BaseModel):
    """会话历史记录列表响应"""
    sessions: list[ChatSessionHistory] = Field(..., description="会话列表")
    total: int = Field(..., description="总数量")


class ChatMessageHistory(BaseModel):
    """消息历史记录响应模型"""
    id: int = Field(..., description="消息ID")
    message: str = Field(..., description="消息内容")
    message_type: str = Field(..., description="消息类型")
    gmt_create: datetime = Field(..., description="创建时间")
    status: int = Field(..., description="状态")
    
    class Config:
        from_attributes = True


class ChatMessagesResponse(BaseModel):
    """会话消息列表响应"""
    session_id: str = Field(..., description="会话ID")
    messages: list[ChatMessageHistory] = Field(..., description="消息列表")
    total: int = Field(..., description="消息总数")