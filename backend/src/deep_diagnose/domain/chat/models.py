"""
聊天领域模型定义
"""
from tortoise.models import Model
from tortoise import fields


class CloudbotAgentChatSession(Model):
    """机器人会话表"""
    
    id = fields.BigIntField(pk=True, description="自增主键")
    session_id = fields.CharField(max_length=64, unique=True, description="会话id")
    user_id = fields.CharField(max_length=16, description="员工工号")
    title = fields.CharField(max_length=512, default="", description="会话标题")
    gmt_create = fields.DatetimeField(auto_now_add=True, description="创建时间")
    gmt_modified = fields.DatetimeField(auto_now=True, description="更新时间")
    ext = fields.CharField(max_length=64, default="", description="扩展字段")
    instance_id = fields.CharField(max_length=64, default="", description="诊断实例id")
    
    # 反向关系
    messages: fields.ReverseRelation["CloudbotAgentChatMessage"]
    
    class Meta:
        table = "cloudbot_agent_chat_session"
        table_description = "机器人会话表"
        indexes = [
            ("user_id",),  # 员工工号索引 (对应数据表中的idx_staff_id)
        ]

    
    def __str__(self):
        return f"<CloudbotAgentChatSession(id={self.id}, session_id='{self.session_id}', user_id='{self.user_id}')>"


class CloudbotAgentChatMessage(Model):
    """机器人聊天记录表"""
    
    id = fields.BigIntField(pk=True, description="自增主键")
    session_id = fields.CharField(max_length=64, description="会话id")
    message = fields.TextField(description="聊天信息")
    message_type = fields.CharField(max_length=16, default="", description="发送者，ai:机器人回复，human:人工回复")
    scenario = fields.CharField(max_length=16, default="", description="场景编码")
    source = fields.IntField(default=0, description="来源，0:pc端机器人会话，1:全链路定界，2:钉钉对话")
    gmt_create = fields.DatetimeField(auto_now_add=True, description="创建时间")
    gmt_modified = fields.DatetimeField(auto_now=True, description="更新时间")
    message_id = fields.BigIntField(default=0, description="问题ID")
    status = fields.SmallIntField(default=0, description="是否完成请求，0:已完成，1未完成")

    class Meta:
        table = "cloudbot_agent_chat_message"
        table_description = "机器人聊天记录表"
        indexes = [
            ("session_id",),  # 会话ID索引
            ("message_id",),  # 消息ID索引
            ("status", "gmt_create"),  # 状态和创建时间复合索引
        ]
    
    def __str__(self):
        return f"<CloudbotAgentChatMessage(id={self.id}, session_id='{self.session_id}', type='{self.message_type}')>"