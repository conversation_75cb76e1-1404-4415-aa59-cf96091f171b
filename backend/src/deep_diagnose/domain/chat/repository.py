"""
聊天领域仓储层
处理聊天会话和消息的数据库操作
"""
import logging
from typing import List, Optional
from uuid import uuid4

from .models import CloudbotAgentChatSession, CloudbotAgentChatMessage
from .schemas import ChatSessionCreate, ChatMessageCreate, ChatMessageUpdate

logger = logging.getLogger(__name__)


class ChatRepository:
    """聊天数据仓储"""
    
    async def create_session(self, user_id: str, title: str = "", instance_id: str = "") -> CloudbotAgentChatSession:
        """创建新的聊天会话
        
        Args:
            user_id: 员工工号
            title: 会话标题
            instance_id: 诊断实例ID
            
        Returns:
            创建的会话对象
        """
        session_id = str(uuid4())
        
        session_data = ChatSessionCreate(
            session_id=session_id,
            user_id=user_id,
            title=title,
            instance_id=""
        )
        
        session = await CloudbotAgentChatSession.create(**session_data.model_dump())
        logger.info(f"Created new chat session: {session.session_id} for user: {user_id}")
        
        return session
    
    async def get_session_by_id(self, session_id: str) -> Optional[CloudbotAgentChatSession]:
        """根据session_id获取会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话对象或None
        """
        return await CloudbotAgentChatSession.filter(session_id=session_id).first()
    
    async def get_session_messages(self, session_id: str) -> List[CloudbotAgentChatMessage]:
        """获取会话的所有消息
        
        Args:
            session_id: 会话ID
            
        Returns:
            消息列表，按创建时间排序
        """
        messages = await CloudbotAgentChatMessage.filter(
            session_id=session_id
        ).order_by('gmt_create')
        
        return messages
    
    async def create_message(
        self, 
        session_id: str, 
        message: str, 
        message_type: str,
        scenario: str = "",
        source: int = 0,
        message_id: int = 0,
        status: int = 0
    ) -> CloudbotAgentChatMessage:
        """创建新消息
        
        Args:
            session_id: 会话ID
            message: 消息内容
            message_type: 消息类型 (human/ai)
            scenario: 场景编码
            source: 来源
            message_id: 问题ID
            status: 状态 (0:已完成，1:未完成)
            
        Returns:
            创建的消息对象
        """
        message_data = ChatMessageCreate(
            session_id=session_id,
            message=message,
            message_type=message_type,
            scenario=scenario,
            source=source,
            message_id=message_id,
            status=status
        )
        
        message_obj = await CloudbotAgentChatMessage.create(**message_data.model_dump())
        logger.info(f"Created message {message_obj.id} for session {session_id}, type: {message_type}")
        
        return message_obj
    
    async def update_message(self, message_id: int, **kwargs) -> Optional[CloudbotAgentChatMessage]:
        """更新消息
        
        Args:
            message_id: 消息ID
            **kwargs: 要更新的字段
            
        Returns:
            更新后的消息对象或None
        """
        message = await CloudbotAgentChatMessage.filter(id=message_id).first()
        if message:
            await message.update_from_dict(kwargs)
            await message.save()
            logger.info(f"Updated message {message_id}")
            return message
        
        logger.warning(f"Message {message_id} not found for update")
        return None
    
    async def update_message_content(self, message_id: int, content: str) -> Optional[CloudbotAgentChatMessage]:
        """更新消息内容
        
        Args:
            message_id: 消息ID
            content: 新的消息内容
            
        Returns:
            更新后的消息对象或None
        """
        return await self.update_message(message_id, message=content)
    
    async def mark_message_completed(self, message_id: int) -> Optional[CloudbotAgentChatMessage]:
        """标记消息为已完成
        
        Args:
            message_id: 消息ID
            
        Returns:
            更新后的消息对象或None
        """
        return await self.update_message(message_id, status=0)
    
    async def get_user_sessions(
        self, 
        user_id: str, 
        start_time: Optional[str] = None, 
        end_time: Optional[str] = None, 
        limit: int = 20
    ) -> List[CloudbotAgentChatSession]:
        """获取用户的聊天会话列表
        
        Args:
            user_id: 用户ID
            start_time: 开始时间 (ISO格式字符串)
            end_time: 结束时间 (ISO格式字符串)
            limit: 返回数量限制
            
        Returns:
            会话列表，按创建时间倒序
        """
        query = CloudbotAgentChatSession.filter(user_id=user_id)
        
        # 添加时间过滤条件
        if start_time:
            from datetime import datetime
            start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            query = query.filter(gmt_create__gte=start_dt)
            
        if end_time:
            from datetime import datetime
            end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            query = query.filter(gmt_create__lte=end_dt)
        
        sessions = await query.order_by('-gmt_create').limit(limit)
        logger.info(f"Retrieved {len(sessions)} sessions for user {user_id}")
        
        return sessions


# 全局仓储实例
chat_repository = ChatRepository()