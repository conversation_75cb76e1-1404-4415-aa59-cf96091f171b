"""
CRUD operations for database models - Tortoise ORM
"""
from typing import Optional, List, Dict, Any


from .models import CloudbotAgentUser
from .schemas import UserCreate, UserUpdate


class UserCRUD:
    """用户CRUD操作 - Tortoise ORM"""


    async def get(user_id: int) -> Optional[CloudbotAgentUser]:
        """根据ID获取用户"""
        return await CloudbotAgentUser.get_or_none(id=user_id)
    



    async def get_by_username(username: str) -> Optional[CloudbotAgentUser]:
        """根据用户名获取用户"""
        return await CloudbotAgentUser.get_or_none(username=username)
    
    @staticmethod
    async def get_multi(skip: int = 0, limit: int = 100) -> List[CloudbotAgentUser]:
        """获取多个用户"""
        return await CloudbotAgentUser.all().offset(skip).limit(limit)
    
    @staticmethod
    async def create(user_data: UserCreate) -> CloudbotAgentUser:
        """创建用户（密码会被哈希）"""
        user = await CloudbotAgentUser.create(
            username=user_data.username,
            password=user_data.password
        )
        return user
    
    @staticmethod
    async def update(user_id: int, user_data: UserUpdate) -> Optional[CloudbotAgentUser]:
        """更新用户"""
        user = await CloudbotAgentUser.get_or_none(id=user_id)
        if not user:
            return None
        
        update_data = user_data.dict(exclude_unset=True)
        if 'password' in update_data:
            update_data['password'] = update_data['password']
        
        await user.update_from_dict(update_data)
        await user.save()
        return user
    
    @staticmethod
    async def delete(user_id: int) -> bool:
        """删除用户"""
        user = await CloudbotAgentUser.get_or_none(id=user_id)
        if user:
            await user.delete()
            return True
        return False
    
    @staticmethod
    async def count() -> int:
        """获取用户总数"""
        return await CloudbotAgentUser.all().count()
    
    @staticmethod
    async def verify_password(username: str, password: str) -> Optional[CloudbotAgentUser]:
        """验证用户密码"""
        user = await CloudbotAgentUser.get_or_none(username=username)
        if user and password == user.password:
            return user
        return None
    
    @staticmethod
    async def update_password(user_id: int, new_password: str) -> Optional[CloudbotAgentUser]:
        """更新用户密码"""
        user = await CloudbotAgentUser.get_or_none(id=user_id)
        if user:
            user.password = new_password
            await user.save()
            return user
        return None



# 创建CRUD实例
user_crud = UserCRUD()