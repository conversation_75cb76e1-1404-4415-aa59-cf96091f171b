### **设计文档：ChatService 与 Agent 架构重构**

#### **1. 目标与原则**

本次重构的核心目标是**解耦** `ChatService` 与核心的智能代理（Agent/Graph）逻辑。当前 `ChatService` 承担了过多的职责，包括状态管理、事件流处理和业务流程编排，导致其功能臃肿、难以维护和扩展。

**重构将遵循以下原则：**

*   **单一职责原则:** `ChatService` 应仅作为 API 入口和请求分发层，不关心 Graph 内部的运行机制和状态。
*   **关注点分离:** 将 Agent 的状态管理、逻辑编排、事件流处理等核心能力**内聚**到 Graph 对象内部。
*   **可扩展性:** 通过引入工厂模式和基类，使系统能够方便地支持未来可能增加的新类型 Agent/Graph。
*   **接口简化:** 对外暴露的接口应保持简洁和稳定。

#### **2. 核心组件设计**

我们将引入以下几个核心组件来构建新的架构：

*   **`ChatService` (重构后):**
    *   **职责:** 作为服务层，负责接收外部请求（如来自 API acontroller），验证用户和会话信息，通过 `GraphFactory` 创建相应的 Graph 实例，并调用 Graph 的方法返回结果。
    *   **特点:** 无状态，不持有任何与 Graph 执行相关的中间状态（如 `plan_iterations`, `final_report` 等）。

*   **`GraphFactory`:**
    *   **职责:** 负责根据指定的 `graph_type` 创建和初始化具体的 Graph 实例。
    *   **特点:** 集中管理 Graph 的创建逻辑，是系统扩展的入口。

*   **`BaseGraph` (ABC - 抽象基类):**
    *   **职责:** 定义所有 Graph 类型必须遵循的统一接口和基础属性。
    *   **接口:** `astream(question: str) -> AsyncIterator[dict]`
    *   **基础属性:** 管理 Graph 运行时的所有状态和配置。

*   **`GraphAgent` (具体实现):**
    *   **职责:** 继承自 `BaseGraph`，实现具体的诊断、规划、执行的 Agent 逻辑。LangChain Graph 的封装和执行将在此类中完成。
    *   **特点:** 自包含，管理自身的完整生命周期，包括状态更新和事件流推送。

#### **3. 拟定的文件结构**

为了实现上述设计，我们将创建新的文件和目录结构：

```
/Users/<USER>/git/ecs-deep-diagnose/backend/src/deep_diagnose/
└── core/
    └── agent/
        ├── __init__.py
        ├── base.py          # 定义 BaseGraph 抽象基类
        ├── factory.py       # 定义 GraphFactory
        └── graph_agent.py   # 定义 GraphAgent 具体实现
```

#### **4. 详细类设计**

##### **4.1. `deep_diagnose.services.chat.chat_service.ChatService` (重构后)**

`ChatService` 将被大幅简化。

```python
# /Users/<USER>/git/ecs-deep-diagnose/backend/src/deep_diagnose/services/chat/chat_service.py

from deep_diagnose.core.agent.factory import GraphFactory

class ChatService:
    """
    服务层，处理聊天相关的主要业务逻辑。
    """
    def __init__(self):
        # ChatService 可以是无状态的，或者只包含必要的依赖注入
        self.graph_factory = GraphFactory()

    async def chat(self, question: str, user_id: str, session_id: str, graph_type: str = "GraphAgent"):
        """
        对外提供的主要聊天接口。

        Args:
            question (str): 用户提出的问题。
            user_id (str): 用户ID。
            session_id (str): 会话ID。
            graph_type (str): 要使用的Graph类型，默认为 "GraphAgent"。

        Yields:
            dict: 从Graph流式返回的事件。
        """
        # 1. (可选) 进行用户、会话相关的业务逻辑处理
        # ...

        # 2. 使用工厂创建 Graph 实例
        # 此处可以传递用户或会话相关的配置
        
        graph = self.graph_factory.create_graph(graph_type)

        # 3. 调用 Graph 的 astream 方法并流式返回结果
        async for event in graph.astream(question=question):
            yield event



```

##### **4.2. `deep_diagnose.core.agent.factory.GraphFactory`**

```python
# /Users/<USER>/git/ecs-deep-diagnose/backend/src/deep_diagnose/core/agent/factory.py

from .base import BaseGraph
from .graph_agent import GraphAgent

class GraphFactory:
    """
    根据类型创建和返回相应的 Graph 实例。
    """
    def create_graph(self, graph_type: str) -> BaseGraph:
        """
        创建 Graph 实例。

        Args:
            graph_type (str): Graph 的类型标识符。
            config (dict | None): 传递给 Graph 实例的配置。

        Returns:
            BaseGraph: 一个具体的 Graph 实例。
            
        Raises:
            ValueError: 如果 graph_type 无效。
        """
        if graph_type == "GraphAgent":
            return GraphAgent()
        # 未来可以扩展其他类型的 Graph
        # elif graph_type == "AnotherAgent":
        #     return AnotherAgent(config=config)
        else:
            raise ValueError(f"未知的 Graph 类型: {graph_type}")

```

##### **4.3. `deep_diagnose.core.agent.base.BaseGraph`**

```python
# /Users/<USER>/git/ecs-deep-diagnose/backend/src/deep_diagnose/core/agent/base.py

import abc
from typing import AsyncIterator

class BaseGraph(abc.ABC):
    """
    所有 Graph Agent 的抽象基类。
    定义了统一的接口和所有 Graph 必须具备的基础属性。
    """
    def __init__(self, config: dict | None = None):
        config = config or {}
        
        # ------------------------------------------------------------------
        # 核心要求：将 Graph 状态和配置作为其内部属性
        # ------------------------------------------------------------------
        
        # 运行时状态
        self.plan_iterations: int = 0
        self.final_report: str = ""
        self.current_plan: list | None = None
        self.observations: list = []
        
        # 从外部传入的配置
        self.auto_accepted_plan: bool = config.get("auto_accepted_plan", False)
        self.enable_background_investigation: bool = config.get("enable_background_investigation", True)
        self.max_step_num: int = config.get("max_step_num", 10)
        self.max_plan_iterations: int = config.get("max_plan_iterations", 3)
        self.max_search_results: int = config.get("max_search_results", 10)
        self.mcp_settings: dict = config.get("mcp_settings", {})

    @abc.abstractmethod
    async def astream(self, question: str) -> AsyncIterator[dict]:
        """
        Graph 的主执行方法，以异步生成器方式流式返回事件。

        Args:
            question (str): 用户输入的问题。

        Yields:
            dict: 包含事件类型和数据的字典。
        """
        # 确保子类实现该方法
        yield {}

```

##### **4.4. `deep_diagnose.core.agent.graph_agent.GraphAgent`**

这是核心逻辑的具体实现，它将包含原 `ChatService` 中的 LangChain Graph 编排逻辑。

```python
# /Users/<USER>/git/ecs-deep-diagnose/backend/src/deep_diagnose/core/agent/graph_agent.py

from typing import AsyncIterator
from .base import BaseGraph

class GraphAgent(BaseGraph):
    """
    基于 LangGraph 的诊断 Agent 实现。
    """
    def __init__(self, config: dict | None = None):
        super().__init__(config)
        
        # 在这里初始化 LangChain Graph、Tools、LLMs 等
        self._graph = self._build_langchain_graph()
        # ... 其他初始化

    def _build_langchain_graph(self):
        # ... 此处是构建 LangChain Graph 的复杂逻辑
        # ... 例如：
        # workflow = StateGraph(AgentState)
        # workflow.add_node("planner", self.plan_step)
        # ...
        # return workflow.compile()
        pass

    async def astream(self, question: str) -> AsyncIterator[dict]:
        """
        执行 LangChain Graph 并流式返回结果。
        """
        # ------------------------------------------------------------------
        # 核心要求：事件流处理在 Graph 内部完成
        # ------------------------------------------------------------------
        
        # 示例：定义一个内部函数来处理 LangChain Graph 的流式输出并转换为标准事件
        async def _stream_handler():
            # 这是伪代码，实际需要根据 LangChain 的 astream_events API 进行适配
            # langgraph_stream = self._graph.astream_events(
            #     {"messages": [("user", question)]},
            #     version="v1"
            # )
            # async for event in langgraph_stream:
            #     # 1. 从 LangChain event 中解析出需要的信息
            #     event_type = event["event"] # e.g., "on_chain_start"
            #     data = event["data"]
            #
            #     # 2. 更新 GraphAgent 的内部状态
            #     if event_type == "on_planner_end":
            #         self.current_plan = data.get("plan")
            #         self.plan_iterations += 1
            #
            #     # 3. 将其转换为标准化的事件格式并 yield
            #     yield {
            #         "event_type": f"graph_{event_type}",
            #         "data": data
            #     }
            pass




```

#### **5. 迁移步骤**

1.  **创建文件结构:** 在 `deep_diagnose/core/` 下创建 `agent` 目录及 `base.py`, `factory.py`, `graph_agent.py` 文件。
2.  **定义基类和工厂:** 将上述设计的代码分别填入 `base.py` 和 `factory.py`。
3.  **迁移 Graph 逻辑:**
    *   将 `ChatService` 中所有与 LangChain Graph 构建、状态管理（`plan_iterations` 等）、事件流处理相关的代码，整体迁移到 `GraphAgent` 类中。
    *   `GraphAgent` 的 `__init__` 方法负责构建 Graph，`astream` 方法负责执行 Graph 并处理事件流。
4.  **重构 ChatService:**
    *   删除 `ChatService` 中的所有旧 Graph 逻辑。
    *   实现新的 `chat` 方法，使其调用 `GraphFactory` 和 `graph.astream`。
5.  **更新调用方:** 检查并更新 API 层（如 `deep_diagnose/api/routes/chat.py`）对 `ChatService.chat` 方法的调用，确保接口匹配。
6.  **测试:** 编写单元测试和集成测试，确保重构后的 `ChatService` 和 `GraphAgent` 能够协同工作，并且功能与重构前保持一致。
