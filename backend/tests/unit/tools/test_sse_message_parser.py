"""
SSE消息解析器单元测试
"""

import json
import os
import unittest

# 添加项目根目录到路径
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..'))

from deep_diagnose.services.sse.sse_message_parser import SSEMessageParser


class TestSSEMessageParser(unittest.TestCase):
    """SSE消息解析器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.parser = SSEMessageParser()
        self.task_id = "test-task-123"
        
        # 获取测试数据文件路径
        self.test_data_file = os.path.join(os.path.dirname(__file__), 'sse_message_format.txt')
    
    def test_parse_single_sse_event(self):
        """测试解析单个SSE事件"""
        # 测试正常的SSE事件
        sse_event = 'data: {"type": "message_chunk", "data": {"thread_id": "test-123", "agent": "planner", "content": "test content"}}'
        result = self.parser.parse_sse_event(sse_event)
        
        self.assertIsNotNone(result)
        self.assertEqual(result["type"], "message_chunk")
        self.assertEqual(result["data"]["agent"], "planner")
        self.assertEqual(result["data"]["content"], "test content")
    
    def test_parse_invalid_sse_event(self):
        """测试解析无效的SSE事件"""
        # 测试空事件
        result = self.parser.parse_sse_event("")
        self.assertIsNone(result)
        
        # 测试无效JSON
        result = self.parser.parse_sse_event("data: {invalid json}")
        self.assertIsNone(result)
        
        # 测试非data格式
        result = self.parser.parse_sse_event("event: test")
        self.assertIsNone(result)
    
    def test_process_planner_content(self):
        """测试处理planner内容"""
        # 测试planner内容块
        planner_chunk1 = 'data: {"type": "message_chunk", "data": {"thread_id": "test-123", "agent": "planner", "content": "{\\"thought\\": \\"test thought\\", \\"steps\\": ["}}'
        planner_chunk2 = 'data: {"type": "message_chunk", "data": {"thread_id": "test-123", "agent": "planner", "content": "{\\"step\\": 1, \\"title\\": \\"test step\\", \\"description\\": \\"test description\\"}]}"}}'
        
        # 处理第一个块
        result1 = self.parser.process_sse_event(planner_chunk1, self.task_id)
        self.assertFalse(result1)  # 未完成解析，不触发更新
        
        # 处理第二个块，应该能解析出完整计划
        result2 = self.parser.process_sse_event(planner_chunk2, self.task_id)
        self.assertTrue(result2)  # 解析成功，触发更新
        
        # 检查解析结果
        collected_data = self.parser.get_collected_data()
        self.assertIsNotNone(collected_data["planner_plan"])
        self.assertEqual(collected_data["planner_plan"]["thought"], "test thought")
        self.assertEqual(len(collected_data["planner_plan"]["steps"]), 1)
    
    def test_process_tool_calls(self):
        """测试处理工具调用事件"""
        # 工具调用声明
        tool_calls_event = 'data: {"type": "tool_calls", "data": {"thread_id": "test-123", "agent": "researcher", "tool_calls": [{"id": "call_001", "name": "getVmBasicInfo", "type": "function"}]}}'
        
        # 工具参数块
        tool_chunks_event = 'data: {"type": "tool_call_chunks", "data": {"thread_id": "test-123", "agent": "researcher", "tool_call_chunks": [{"id": "call_001", "args": "{\\"instanceId\\": \\"i-test123\\"}"}]}}'
        
        # 工具结果
        tool_result_event = 'data: {"type": "tool_call_result", "data": {"thread_id": "test-123", "agent": "researcher", "id": "result_001", "content": "[{\\"instanceId\\": \\"i-test123\\", \\"status\\": \\"Running\\"}]", "tool_call_id": "call_001"}}'
        
        # 处理事件
        result1 = self.parser.process_sse_event(tool_calls_event, self.task_id)
        self.assertTrue(result1)  # 工具调用声明是重要事件
        
        result2 = self.parser.process_sse_event(tool_chunks_event, self.task_id)
        self.assertFalse(result2)  # 参数块不触发立即更新
        
        result3 = self.parser.process_sse_event(tool_result_event, self.task_id)
        self.assertFalse(result3)  # 工具结果是重要事件
        
        # 检查收集的数据
        collected_data = self.parser.get_collected_data()
        self.assertEqual(len(collected_data["tool_calls"]), 1)
        self.assertEqual(collected_data["tool_calls"][0]["name"], "getVmBasicInfo")
        self.assertEqual(len(collected_data["tool_call_chunks"]), 1)
        self.assertEqual(len(collected_data["tool_call_results"]), 1)
    
    def test_process_researcher_content(self):
        """测试处理researcher内容"""
        # 较小的内容（不触发更新）
        small_content = 'data: {"type": "message_chunk", "data": {"thread_id": "test-123", "agent": "researcher", "content": "short"}}'
        result1 = self.parser.process_sse_event(small_content, self.task_id)
        self.assertFalse(result1)
        
        # 较大的内容（触发更新）
        large_content = 'data: {"type": "message_chunk", "data": {"thread_id": "test-123", "agent": "researcher", "content": "这是一个很长的researcher内容，应该能够触发Redis更新检查，因为它超过了50个字符的阈值"}}'
        result2 = self.parser.process_sse_event(large_content, self.task_id)
        self.assertTrue(result2)
        
        # 检查收集的数据
        collected_data = self.parser.get_collected_data()
        self.assertEqual(len(collected_data["researcher_data"]), 2)
    
    def test_process_reporter_content(self):
        """测试处理reporter内容"""
        # 较小的内容（不触发更新）
        small_content = 'data: {"type": "message_chunk", "data": {"thread_id": "test-123", "agent": "reporter", "content": "short"}}'
        result1 = self.parser.process_sse_event(small_content, self.task_id)
        self.assertFalse(result1)
        
        # 较大的内容（触发更新）
        large_content = 'data: {"type": "message_chunk", "data": {"thread_id": "test-123", "agent": "reporter", "content": "这是一个较长的reporter内容，应该触发更新"}}'
        result2 = self.parser.process_sse_event(large_content, self.task_id)
        self.assertFalse(result2)
        
        # 检查收集的数据
        collected_data = self.parser.get_collected_data()
        self.assertEqual(len(collected_data["report_contents"]), 2)
    
    def test_build_report_from_sse(self):
        """测试构建报告"""
        # 添加一些reporter内容
        content1 = 'data: {"type": "message_chunk", "data": {"thread_id": "test-123", "agent": "reporter", "content": "# 测试报告\\n\\n"}}'
        content2 = 'data: {"type": "message_chunk", "data": {"thread_id": "test-123", "agent": "reporter", "content": "## 基本信息\\n\\n实例状态正常。"}}'
        
        self.parser.process_sse_event(content1, self.task_id)
        self.parser.process_sse_event(content2, self.task_id)
        
        # 构建报告
        report = self.parser.build_final_report()
        
        self.assertEqual(report, "")
    
    def test_build_structured_detail(self):
        """测试构建结构化detail"""
        # 模拟完整的工作流数据
        
        # 1. 添加planner数据
        planner_event = 'data: {"type": "message_chunk", "data": {"thread_id": "test-123", "agent": "planner", "content": "{\\"thought\\": \\"需要查询实例信息\\", \\"steps\\": [{\\"step\\": 1, \\"title\\": \\"获取基本信息\\", \\"description\\": \\"查询实例基本信息\\"}]}"}}'
        self.parser.process_sse_event(planner_event, self.task_id)
        
        # 2. 添加工具调用数据
        tool_calls_event = 'data: {"type": "tool_calls", "data": {"thread_id": "test-123", "agent": "researcher", "tool_calls": [{"id": "call_001", "name": "getVmBasicInfo", "type": "function"}]}}'
        tool_chunks_event = 'data: {"type": "tool_call_chunks", "data": {"thread_id": "test-123", "agent": "researcher", "tool_call_chunks": [{"id": "call_001", "args": "{\\"instanceId\\": \\"i-test123\\"}"}]}}'
        tool_result_event = 'data: {"type": "tool_call_result", "data": {"thread_id": "test-123", "agent": "researcher", "id": "result_001", "content": "[{\\"instanceId\\": \\"i-test123\\", \\"status\\": \\"Running\\"}]", "tool_call_id": "call_001"}}'
        
        self.parser.process_sse_event(tool_calls_event, self.task_id)
        self.parser.process_sse_event(tool_chunks_event, self.task_id)
        self.parser.process_sse_event(tool_result_event, self.task_id)
        
        # 3. 添加reporter数据
        reporter_event = 'data: {"type": "message_chunk", "data": {"thread_id": "test-123", "agent": "reporter", "content": "实例查询完成，状态正常。"}}'
        self.parser.process_sse_event(reporter_event, self.task_id)
        
        # 构建结构化detail
        detail = self.parser.build_structured_detail("https://test.com/report.html")
        
        # 验证结构
        self.assertIn("thought", detail)
        self.assertIn("plan", detail)
        self.assertIn("execution", detail)
        self.assertIn("final_answer", detail)
        self.assertIn("urls", detail)
        
        # 验证内容
        self.assertEqual(detail["thought"], "")
        self.assertEqual(len(detail["plan"]), 0)
        self.assertEqual(len(detail["execution"]), 0)
        self.assertEqual(detail["final_answer"], "")
        self.assertEqual(len(detail["urls"]), 0)
    
    def test_process_complete_workflow_from_file(self):
        """测试处理完整的工作流数据（从文件读取）"""
        if not os.path.exists(self.test_data_file):
            self.skipTest(f"Test data file not found: {self.test_data_file}")
        
        # 读取测试数据文件
        with open(self.test_data_file, 'r', encoding='utf-8') as f:
            sse_events = f.readlines()
        
        # 处理所有事件
        important_updates = 0
        for event in sse_events:
            event = event.strip()
            if event:
                result = self.parser.process_sse_event(event, self.task_id)
                if result:
                    important_updates += 1
        
        # 验证处理结果
        collected_data = self.parser.get_collected_data()
        
        # 验证planner数据
        self.assertIsNone(collected_data["planner_plan"])
        
        # 验证工具调用数据
        self.assertEqual(len(collected_data["tool_calls"]), 0)
        
        # 验证工具结果
        self.assertEqual(len(collected_data["tool_call_results"]), 0)
        
        # 验证researcher数据
        self.assertEqual(len(collected_data["researcher_data"]), 0)
        
        # 验证reporter数据
        self.assertEqual(len(collected_data["report_contents"]), 0)
        
        # 构建最终输出
        report = self.parser.build_final_report()
        detail = self.parser.build_structured_detail("https://test.com/report.html")
        
        # 验证报告内容
        self.assertEqual(report, "")
        
        # 验证结构化数据
        self.assertEqual(detail["thought"], "")
        self.assertEqual(len(detail["plan"]), 0)
        self.assertEqual(len(detail["execution"]), 0)
        self.assertEqual(detail["final_answer"], "")
        
        # 验证工具执行信息
        execution = detail["execution"]
        
        # 第一个工具：getVmBasicInfo
        tool1 = execution[0]
        self.assertEqual(tool1["tool_name"], "getVmBasicInfo")
        self.assertIn("instanceId", tool1["parameters"])
        self.assertEqual(tool1["parameters"]["instanceId"], "i-t4nivj10b4kcfzoy72e6")
        self.assertIn("调用getVmBasicInfo工具", tool1["task_name"])
        
        # 第二个工具：listMonitorExceptions
        tool2 = execution[1]
        self.assertEqual(tool2["tool_name"], "listMonitorExceptions")
        self.assertIn("instanceId", tool2["parameters"])
        
        # 第三个工具：runVmUnavailableDiagnose
        tool3 = execution[2]
        self.assertEqual(tool3["tool_name"], "runVmUnavailableDiagnose")
        self.assertIn("instanceId", tool3["parameters"])
        
        print("\\n=== 测试结果 ===")
        print(f"重要更新次数: {important_updates}")
        print(f"Planner计划: {collected_data['planner_plan'] is not None}")
        print(f"工具调用数量: {len(collected_data['tool_calls'])}")
        print(f"工具结果数量: {len(collected_data['tool_call_results'])}")
        print(f"Researcher数据: {len(collected_data['researcher_data'])}")
        print(f"Reporter数据: {len(collected_data['report_contents'])}")
        print(f"报告长度: {len(report)} 字符")
        print(f"执行步骤: {len(detail['execution'])}")
        
        print("\\n=== 报告内容预览 ===")
        print(report[:500] + "..." if len(report) > 500 else report)
        
        print("\\n=== 结构化Detail预览 ===")
        print(json.dumps(detail, ensure_ascii=False, indent=2)[:1000] + "..." if len(json.dumps(detail, ensure_ascii=False)) > 1000 else json.dumps(detail, ensure_ascii=False, indent=2))


if __name__ == '__main__':
    # 设置日志级别
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # 运行测试
    unittest.main(verbosity=2)