import unittest
import sys
import os

# 使用正确的导入路径
from deep_diagnose.services.report_generator.processors import HTMLContentProcessor

class TestHTMLContentProcessor(unittest.TestCase):

    def setUp(self):
        # HTMLContentProcessor doesn't use config in its current methods, so an empty dict is fine
        self.processor = HTMLContentProcessor()

    def test_markdown_to_html_conversion(self):
        markdown_input = """# 批量实例不可用问题排查报告

## 诊断信息
实例ID:  
i-t4n4vky24zw2w1qnqoyf  
i-t4n74bsfzx58x0lj4qbh  
i-t4na3cc0c9mimcw9667x  
i-j6ch2zf4qfy1rltbql6r  
i-2vc5alcmxz75rw8aol4g  
i-2vc0zliaw8ilg744cdrq  
i-2vc6qv34j96hkmcwms5d  

时间范围: 2025-06-26 01:00:00 至 2025-06-26 04:00:00  
问题描述: 用户报告多个ECS实例在指定时间段内发生不可用问题

## 关键要点

1. 物理机分布分析表明，7个ECS实例分布在不同的物理服务器（NC）上，不存在集中化故障风险。
2. 使用 runVmUnavailableDiagnose 工具进行逐一诊断，结果显示所有实例在该时间段内未检测到可用性问题。
3. 通过 listChangeRecords 工具发现一次高危变更操作：procedure-gear-503299387-638189262，由用户徐云提交，在 2025-06-26 03:47:07 执行，导致所有7个实例出现 VSOCK_ICMP_PING 全丢包现象。

## 推断过程

1. **物理机聚集性检查**
   查询所有7个实例的物理机归属情况，确认它们分别位于以下IP地址：
   - *************
   - *************
   - **************
   - *************
   - ************
   - *************
   - **************
   
   结论：这些实例分布在不同物理机上，排除了因单一物理机故障导致批量不可用的可能性。

2. **实例可用性诊断**
   使用 runVmUnavailableDiagnose 工具对每个实例进行诊断，结果均为 null。
   
   结论：在指定时间段内，未检测到任何实例出现不可用状态，初步判断服务中断可能并非由底层故障引起。

3. **高危变更记录核查**
   通过 listChangeRecords 查询到一次关键变更事件：
   - 变更任务ID：procedure-gear-503299387-638189262
   - 提交用户：徐云
   - 服务名称：EcsVirtVMUpdateEngine.HotUpgradeCmd#
   - 时间戳：2025-06-26 03:47:07
   - 影响范围：全部7个实例
   - 描述：导致 VSOCK_ICMP_PING 全丢包

   结论：唯一检测到的系统级变更与用户报告的时间段高度吻合，可能是造成感知层面“不可用”现象的原因。

## 总结及建议

综合各项诊断结果：

1）7个实例分布在不同的物理机上，排除了物理层聚集性故障；
2）runVmUnavailableDiagnose 诊断未发现不可用状态；
3）检测到一次热升级命令执行，导致 VSOCK_ICMP_PING 全丢包，可能是用户感知不可用的根本原因。

建议进一步采取以下措施：

- 深入调查 procedure-gear-503299387-638189262 的具体变更内容、预期影响范围和实际表现；
- 验证该热升级是否引入了虚拟化层网络异常，特别是与 VSOCK_ICMP_PING 相关的模块；
- 分析受影响实例的监控指标（如 ping 响应延迟、CPU、内存、网络 I/O 等）以辅助定位问题；
- 若该变更为非预期或存在副作用，建议评估是否需要回滚或发布修复补丁；
- 加强变更前的风险评估机制，并确保变更通知流程完整有效，避免用户误判为服务中断。"""
        
        expected_html = """<h1>批量实例不可用问题排查报告</h1>

<h2>诊断信息</h2>
<p>实例ID:</p>
<p>i-t4n4vky24zw2w1qnqoyf</p>
<p>i-t4n74bsfzx58x0lj4qbh</p>
<p>i-t4na3cc0c9mimcw9667x</p>
<p>i-j6ch2zf4qfy1rltbql6r</p>
<p>i-2vc5alcmxz75rw8aol4g</p>
<p>i-2vc0zliaw8ilg744cdrq</p>
<p>i-2vc6qv34j96hkmcwms5d</p>

<p>时间范围: 2025-06-26 01:00:00 至 2025-06-26 04:00:00</p>
<p>问题描述: 用户报告多个ECS实例在指定时间段内发生不可用问题</p>

<h2>关键要点</h2>
<ol>
  <li>物理机分布分析表明，7个ECS实例分布在不同的物理服务器（NC）上，不存在集中化故障风险。</li>
  <li>使用 runVmUnavailableDiagnose 工具进行逐一诊断，结果显示所有实例在该时间段内未检测到可用性问题。</li>
  <li>通过 listChangeRecords 工具发现一次高危变更操作：procedure-gear-503299387-638189262，由用户徐云提交，在 2025-06-26 03:47:07 执行，导致所有7个实例出现 VSOCK_ICMP_PING 全丢包现象。</li>
</ol>

<h2>推断过程</h2>

<h2>物理机聚集性检查</h2>
<p>查询所有7个实例的物理机归属情况，确认它们分别位于以下IP地址：</p>
<ul>
  <li>*************</li>
  <li>*************</li>
  <li>**************</li>
  <li>*************</li>
  <li>************</li>
  <li>*************</li>
  <li>**************</li>
</ul>

<p>结论：这些实例分布在不同物理机上，排除了因单一物理机故障导致批量不可用的可能性。</p>

<h2>实例可用性诊断</h2>
<p>使用 runVmUnavailableDiagnose 工具对每个实例进行诊断，结果均为 null。</p>

<p>结论：在指定时间段内，未检测到任何实例出现不可用状态，初步判断服务中断可能并非由底层故障引起。</p>

<h2>高危变更记录核查</h2>
<p>通过 listChangeRecords 查询到一次关键变更事件：</p>
<ul>
  <li>变更任务ID：procedure-gear-503299387-638189262</li>
  <li>提交用户：徐云</li>
  <li>服务名称：EcsVirtVMUpdateEngine.HotUpgradeCmd#</li>
  <li>时间戳：2025-06-26 03:47:07</li>
  <li>影响范围：全部7个实例</li>
  <li>描述：导致 VSOCK_ICMP_PING 全丢包</li>
</ul>

<p>结论：唯一检测到的系统级变更与用户报告的时间段高度吻合，可能是造成感知层面“不可用”现象的原因。</p>

<h2>总结及建议</h2>

<p>1）7个实例分布在不同的物理机上，排除了物理层聚集性故障；</p>
<p>2）runVmUnavailableDiagnose 诊断未发现不可用状态；</p>
<p>3）检测到一次热升级命令执行，导致 VSOCK_ICMP_PING 全丢包，可能是用户感知不可用的根本原因。</p>

<p>建议进一步采取以下措施：</p>
<ul>
  <li>深入调查 procedure-gear-503299387-638189262 的具体变更内容、预期影响范围和实际表现；</li>
  <li>验证该热升级是否引入了虚拟化层网络异常，特别是与 VSOCK_ICMP_PING 相关的模块；</li>
  <li>分析受影响实例的监控指标（如 ping 响应延迟、CPU、内存、网络 I/O 等）以辅助定位问题；</li>
  <li>若该变更为非预期或存在副作用，建议评估是否需要回滚或发布修复补丁；</li>
  <li>加强变更前的风险评估机制，并确保变更通知流程完整有效，避免用户误判为服务中断。</li>
</ul>"""
        
        generated_html = self.processor.process_content(markdown_input)

        with open("generated_html.html", "w") as f:
            f.write(generated_html)
        with open("expected_html.html", "w") as f:
            f.write(expected_html)

        self.assertEqual(generated_html, expected_html)

if __name__ == '__main__':
    unittest.main()
