import os

import pytest

from deep_diagnose.common.config.constants.agents import AGENT_LLM_MAP
from deep_diagnose.common.config.constants.app import APP_HOME_DIR
from deep_diagnose.common.config.constants.mcp import get_mcp_settings, get_default_headers
from deep_diagnose.common.config.constants.questions import BUILT_IN_QUESTIONS, BUILT_IN_QUESTIONS_ZH_CN
from deep_diagnose.common.config.constants.tools import SELECTED_SEARCH_ENGINE, SearchEngine
from deep_diagnose.common.config.constants.workflow import DEFAULT_MAX_STEP_NUM, DEFAULT_AUTO_ACCEPTED_PLAN,     DEFAULT_ENABLE_BACKGROUND_INVESTIGATION


@pytest.fixture(autouse=True)
def cleanup_env_vars():
    """Fixture to clean up environment variables after each test."""
    original_env = os.environ.copy()
    yield
    # Restore original environment variables
    os.environ.clear()
    os.environ.update(original_env)


class TestConstants:
    """Test cases for common.constants module."""

    def test_app_constants_exist(self):
        """Test that basic app constants are defined."""
        assert APP_HOME_DIR is not None
        assert isinstance(APP_HOME_DIR, str)

    def test_workflow_constants_exist(self):
        """Test that workflow constants are defined with correct types."""
        assert isinstance(DEFAULT_MAX_STEP_NUM, int)
        assert isinstance(DEFAULT_AUTO_ACCEPTED_PLAN, bool)
        assert isinstance(DEFAULT_ENABLE_BACKGROUND_INVESTIGATION, bool)

    def test_mcp_settings_structure(self):
        """Test that MCP settings have correct structure."""
        mcp_settings_val = get_mcp_settings()
        assert isinstance(mcp_settings_val, dict)
        assert "servers" in mcp_settings_val
        assert isinstance(mcp_settings_val["servers"], dict)

        # Test each server configuration
        for server_name, server_config in mcp_settings_val["servers"].items():
            assert isinstance(server_name, str)
            assert isinstance(server_config, dict)
            assert "name" in server_config
            assert "transport" in server_config
            assert "enabled_tools" in server_config
            assert "add_to_agents" in server_config

    def test_headers_structure(self):
        """Test that headers have correct structure."""
        headers_val = get_default_headers()
        assert isinstance(headers_val, dict)
        assert "Content-Type" in headers_val
        assert "Authorization" in headers_val


class TestAgentsConfig:
    """Test cases for config.agents module."""

    def test_agent_llm_map_structure(self):
        """Test AGENT_LLM_MAP structure."""
        assert isinstance(AGENT_LLM_MAP, dict)

        valid_llm_types = ["basic", "reasoning", "vision"]
        for agent, llm_type in AGENT_LLM_MAP.items():
            assert isinstance(agent, str)
            assert llm_type in valid_llm_types


class TestToolsConfig:
    """Test cases for config.tools module."""

    def test_search_engine_enum(self):
        """Test SearchEngine enum."""
        assert SearchEngine.TAVILY.value == "tavily"
        assert SearchEngine.DUCKDUCKGO.value == "duckduckgo"
        assert SearchEngine.BRAVE_SEARCH.value == "brave_search"
        assert SearchEngine.ARXIV.value == "arxiv"

    def test_selected_search_engine(self):
        """Test SELECTED_SEARCH_ENGINE configuration."""
        assert SELECTED_SEARCH_ENGINE is not None
        assert isinstance(SELECTED_SEARCH_ENGINE, str)


class TestQuestionsConfig:
    """Test cases for config.questions module."""

    def test_built_in_questions_structure(self):
        """Test built-in questions structure."""
        assert isinstance(BUILT_IN_QUESTIONS, list)
        assert len(BUILT_IN_QUESTIONS) > 0
        assert all(isinstance(q, str) for q in BUILT_IN_QUESTIONS)

        assert isinstance(BUILT_IN_QUESTIONS_ZH_CN, list)
        assert len(BUILT_IN_QUESTIONS_ZH_CN) > 0
        assert all(isinstance(q, str) for q in BUILT_IN_QUESTIONS_ZH_CN)

    def test_questions_count_match(self):
        """Test that English and Chinese questions have same count."""
        assert len(BUILT_IN_QUESTIONS) == len(BUILT_IN_QUESTIONS_ZH_CN)
