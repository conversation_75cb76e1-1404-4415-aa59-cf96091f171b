"""
测试任务API路由
"""
import json
import os
import time

import httpx
import pytest
from fastapi import status

os.environ.setdefault('APP_ENV', 'test')
@pytest.mark.asyncio
async def test_task_api():
    timeout = httpx.Timeout(600.0, connect=5.0)

    """测试用户名密码验证"""
    async with httpx.AsyncClient(base_url="http://localhost:8000", timeout=timeout) as client:
        # 构建请求数据
        request_data = {
            "access_key": "admin",
            "secret_key": "admin",
            "token_lifetime_minutes": 60
        }

        # 发送POST请求到验证API
        response = await client.post(
            "/api/token",
            json=request_data
        )
        assert response.status_code == status.HTTP_200_OK
        content = json.loads(response.content)
        assert content is not None

        next_request_data = {
            "agent": "DiagnoseAgent",
            # "question": "诊断 i-bp131jfclul2mokez67x 在2025-05-21的 重启原因？"
            "question": "查询*************物理机硬件规格配置"
        }

        # 发送POST请求到聊天流式API
        next_response = await client.post(
            "/api/v1/tasks",
            json=next_request_data,
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer " + content.get("access_token")
            }
        )

        # 验证响应状态码
        assert next_response.status_code == status.HTTP_202_ACCEPTED
        data = next_response.json()
        assert "task_id" in data

        # 发送POST请求到聊天流式API
        status_response = await client.get(
            "/api/v1/tasks/" + data["task_id"] + "/status",
            headers={
                "Content-Type": "application/json",
                "Authorization": "Bearer " + content.get("access_token")
            }
        )

        # 验证响应状态码
        assert status_response.status_code == status.HTTP_200_OK
        status_data = status_response.json()
        assert status_data["status"] == "PROCESSING"
        task_status = status_data["status"]
        while task_status == "PROCESSING":
            # 发送POST请求到聊天流式API
            last_response = await client.get(
                "/api/v1/tasks/" + data["task_id"],
                headers={
                    "Content-Type": "application/json",
                    "Authorization": "Bearer " + content.get("access_token")
                }
            )
            # 验证响应状态码
            assert last_response.status_code == status.HTTP_200_OK
            last_data = last_response.json()
            task_status = last_data["status"]
            if task_status != "SUCCESS":
                time.sleep(180)

        assert last_data["data"] is not None
        assert last_data["data"]["result"] is not None
        assert last_data["data"]["detail"] is not None
