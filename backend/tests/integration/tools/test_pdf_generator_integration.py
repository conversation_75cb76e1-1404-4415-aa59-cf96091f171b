import os
import tempfile

from deep_diagnose.services.report_generator import create_pdf_generator


class TestPDFGeneratorIntegration:
    """测试PDF生成器集成功能"""
    
    def test_create_pdf_generator(self):
        """测试PDF生成器创建"""
        generator1 = create_pdf_generator()
        generator2 = create_pdf_generator()
        
        # 应该返回不同的实例（不再是单例）
        assert generator1 is not generator2
        assert generator1.format.value == generator2.format.value
    
    def test_generate_report_with_chinese_content(self):
        """测试生成包含中文内容的报告"""
        with tempfile.TemporaryDirectory() as temp_dir:
            generator = create_pdf_generator(output_dir=temp_dir, enable_oss=False)
            
            # 包含中文的内容
            result = """## 诊断结果\n\n### 系统状态分析\n系统运行正常，但存在以下问题：\n\n1. **CPU使用率**: 当前使用率为85%，建议优化\n2. **内存使用**: 内存使用率达到90%，需要关注\n3. **磁盘空间**: 剩余空间充足\n\n### 建议措施\n- 优化应用程序性能\n- 增加内存容量\n- 定期清理临时文件\n"""
            
            detail = """## 执行过程\n\n### 数据收集阶段\n成功收集了系统性能数据\n\n### 分析阶段  \n对收集的数据进行了深入分析\n\n### 报告生成阶段\n生成了详细的诊断报告\n"""
            
            pdf_path = generator.generate_full_report(
                task_id="chinese-test-task",
                agent="中文诊断代理",
                question="请分析系统性能状况",
                result=result,
                detail=detail,
                submitted_at="2023-10-27T10:00:00Z",
                completed_at="2023-10-27T10:05:00Z"
            )
            
            # 验证PDF生成成功
            assert pdf_path is not None
            
            # 检查是否是OSS URL还是本地路径
            if pdf_path.startswith('http'):
                # OSS URL - 检查本地文件是否存在
                import glob
                local_files = glob.glob(os.path.join(temp_dir, "diagnostic_report_chinese-test-task_*.pdf"))
                assert len(local_files) > 0, "Local PDF file should exist even when OSS upload succeeds"
                assert os.path.getsize(local_files[0]) > 0
            else:
                # 本地路径
                assert os.path.exists(pdf_path)
                assert os.path.getsize(pdf_path) > 0
