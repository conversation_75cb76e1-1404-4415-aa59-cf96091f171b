import os
import tempfile

import pytest

from deep_diagnose.services.report_generator import create_pdf_generator, is_pdf_generation_available


class TestPDFGeneratorECSDiagnostic:
    """测试PDF生成器 - ECS诊断报告样式"""
    
    @pytest.fixture
    def temp_dir(self):
        """创建临时目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    @pytest.fixture
    def pdf_service(self, temp_dir):
        """创建PDF生成器服务实例"""
        if not is_pdf_generation_available():
            pytest.skip("reportlab not available")
        return PDFGeneratorService(output_dir=temp_dir, enable_oss=False)
    
    def test_ecs_diagnostic_report_generation(self, pdf_service, temp_dir):
        """测试ECS诊断报告PDF生成 - 验证样式改进效果"""
        # Arrange - 真实的ECS诊断报告内容
        ecs_diagnostic_content = """# i-bp131jfclul2mokez67x 实例重启深度诊断报告

## 诊断信息
实例ID: i-bp131jfclul2mokez67x  
时间范围: 2025-05-20 00:00:00 至 2025-05-22 23:59:59  
问题描述: 实例发生非预期重启事件

## 关键要点

通过分析 **get_vm_coredump** 工具输出，确认崩溃原因为用户执行 **sysrq-c** 或写入 **/proc/sysrq-trigger** 文件触发的内核 **panic** 异常。异常发生时间为 **2025-05-21T19:25:54+08:00**。  
**listMonitorExceptions** 工具检测到 **2** 次 "VM内部重启" 异常事件，发生时间为 **2025-05-21 19:25:03**。  
控制台操作日志（通过 **listActionTrail**）和客户侧运维事件（通过 **listReportedOperationalEvents**）均未发现任何平台侧发起的重启或通知。

## 推断过程

### 1. NC节点异常核查
通过 **listMonitorExceptions** 查询 NC 节点 (IP: ****************) 监控异常事件，检测到 **11** 次 "VM内部重启" 异常事件，最早发生于 **2025-05-20 04:30:03**，最晚发生于 **2025-05-22 20:06:59**。

通过 **listOperationRecords** 和 **listChangeRecords** 查询 NC 节点运维规则命中记录和变更记录，结果均为空。

确认 NC 节点层面没有主动发起导致实例重启的运维操作或配置变更。

### 2. 实例层面操作核查
通过 **listActionTrail** 查询控制台操作日志，检测到 `RunCommand`、`CreateNetworkInterface`、`CreateDisk`、`RunInstances` 等操作，但未发现 `RebootInstance` 等重启相关操作。

通过 **listReportedOperationalEvents** 查询客户侧运维事件，结果为空。

排除平台侧主动发起的重启操作可能性。

### 3. 内核崩溃日志分析
通过 **get_vm_coredump** 分析，检测到 `GuestOS.Panic.SysrqTrigger` 异常，确认是由用户主动触发的 **sysrq** 导致的内核 **panic**。

异常时间戳为 **2025-05-21T19:25:54+08:00**，与 **listMonitorExceptions** 检测到的 VM内部重启事件时间高度吻合。

系统调用栈信息明确指向用户主动触发的 **sysrq** 操作。

### 4. 平台诊断工具验证
**runDiagnose** 工具诊断结果显示 **PANIC异常**，异常发生时间为 **2025-05-21 19:26:00**，异常原因代码为 `vm_kernel:panic:fatal`。

结合 **coredump** 分析结果，确认异常根本原因为虚拟机操作系统层面的用户操作导致。

## 总结及建议

综合各项诊断结果：

1）排除了 NC 节点故障、平台运维操作等外部因素；

2）确认从系统日志中识别出用户主动触发的 **sysrq-c** 或 **/proc/sysrq-trigger** 操作导致内核 **panic**；

3）核心证据包括：**get_vm_coredump** 的调用栈分析、**runDiagnose** 的异常诊断、**listMonitorExceptions** 的重启事件记录。

### 建议进一步检查以下方面：

1）排查用户自身的运维操作日志，确认是否有误操作或自动化脚本意外触发此类行为；

2）审查系统权限管理策略，限制对 **/proc/sysrq-trigger** 文件的访问权限；

3）考虑禁用不必要的 **sysrq** 功能，或在生产环境中设置适当的 **sysrq** 触发保护机制；

4）加强系统监控，对异常的内核 **panic** 事件进行实时告警。

如该行为非预期，请加强权限管理和脚本审计以防止类似情况再次发生。可参考 [阿里云官方文档](https://help.aliyun.com/zh/ecs/support/troubleshoot-instance-panic-issues) 进一步了解和预防类似情况。
"""

        detail_content = """# 详细诊断执行过程

## 工具执行记录

### 监控异常事件查询

#### listMonitorExceptions 执行结果

```json
{
  "RequestId": "A1B2C3D4-E5F6-7890-ABCD-EF1234567890",
  "TotalCount": 11,
  "MonitorExceptions": [
    {
      "ExceptionId": "me-bp1234567890abcdef",
      "ExceptionType": "VM内部重启",
      "ExceptionTime": "2025-05-21T19:25:03+08:00",
      "InstanceId": "i-bp131jfclul2mokez67x",
      "NodeIp": "************",
      "ExceptionLevel": "Critical"
    }
  ]
}
```

### 操作记录查询

#### listActionTrail 执行结果

```json
{
  "RequestId": "B2C3D4E5-F6G7-8901-BCDE-F23456789012",
  "TotalCount": 15,
  "ActionTrails": [
    {
      "EventTime": "2025-05-21T18:30:15+08:00",
      "EventName": "RunCommand",
      "UserName": "<EMAIL>",
      "SourceIpAddress": "*************",
      "UserAgent": "AlibabaCloud-Console"
    }
  ]
}
```

### 核心转储分析

#### get_vm_coredump 分析结果

```
Crash Analysis Report
=====================

Crash Time: 2025-05-21T19:25:54+08:00
Crash Type: GuestOS.Panic.SysrqTrigger
Kernel Version: 3.10.0-1160.el7.x86_64

Exception Details:
- Trigger: Manual sysrq-c command
- Process: bash (PID: 12345)
- User: root (UID: 0)
- Working Directory: /root

Call Stack:
[<ffffffff81063930>] sysrq_handle_crash+0x10/0x20
[<ffffffff81063d5a>] __handle_sysrq+0x12a/0x1a0
[<ffffffff81063e47>] write_sysrq_trigger+0x37/0x40
[<ffffffff811f2f88>] proc_reg_write+0x68/0xa0
[<ffffffff8118856a>] vfs_write+0xba/0x1e0
[<ffffffff81188924>] sys_write+0x54/0xa0
[<ffffffff8100b072>] system_call_fastpath+0x16/0x1b
```

### 系统诊断工具验证

#### runDiagnose 执行结果

```xml
<?xml version="1.0" encoding="UTF-8"?>
<DiagnoseResult>
  <RequestId>C3D4E5F6-G7H8-9012-CDEF-************</RequestId>
  <InstanceId>i-bp131jfclul2mokez67x</InstanceId>
  <DiagnoseTime>2025-05-22T10:30:00+08:00</DiagnoseTime>
  <Status>Completed</Status>
  <Results>
    <Exception>
      <Type>PANIC</Type>
      <Time>2025-05-21T19:26:00+08:00</Time>
      <Code>vm_kernel:panic:fatal</Code>
      <Description>Kernel panic triggered by sysrq</Description>
      <Severity>Critical</Severity>
    </Exception>
  </Results>
</DiagnoseResult>
```

## 预防措施

### 系统配置加固

```bash
#!/bin/bash
# 系统安全加固脚本

# 禁用sysrq功能
echo "kernel.sysrq = 0" >> /etc/sysctl.conf
sysctl -p

# 限制/proc/sysrq-trigger访问权限
chmod 600 /proc/sysrq-trigger
chown root:root /proc/sysrq-trigger

# 添加审计规则
echo "-w /proc/sysrq-trigger -p wa -k sysrq_access" >> /etc/audit/rules.d/sysrq.rules
service auditd restart
```

> **重要提示**: 在生产环境中，建议将sysrq功能完全禁用，以防止意外触发系统崩溃。
"""

        task_id = "ecs-diagnostic-test"
        agent = "ECS诊断代理"
        question = "ECS实例重启深度诊断 - 测试PDF样式改进效果"
        submitted_at = "2025-05-22T10:00:00Z"
        completed_at = "2025-05-22T12:00:00Z"

        # Act - 生成PDF报告
        result = pdf_service.generate_diagnostic_report(
            task_id=task_id,
            agent=agent,
            question=question,
            result=ecs_diagnostic_content,
            detail=detail_content,
            submitted_at=submitted_at,
            completed_at=completed_at
        )

        # Assert - 验证PDF生成结果
        assert result.success, f"PDF生成失败: {result.error_message}"
        assert result.file_path is not None, "PDF文件路径不应为空"
        assert result.file_size is not None, "PDF文件大小不应为空"
        assert result.file_size > 0, "PDF文件大小应大于0"
        
        # 验证文件确实存在
        assert os.path.exists(result.file_path), f"PDF文件不存在: {result.file_path}"
        
        # 验证文件大小合理 (应该包含大量内容)
        actual_size = os.path.getsize(result.file_path)
        assert actual_size > 50000, f"PDF文件大小过小: {actual_size} bytes，可能内容渲染不完整"
        assert actual_size == result.file_size, "返回的文件大小与实际文件大小不匹配"
        
        # 验证文件名格式
        expected_filename_pattern = f"diagnostic_report_{task_id}_"
        assert expected_filename_pattern in os.path.basename(result.file_path), \
            f"文件名格式不正确: {os.path.basename(result.file_path)}"
        
        # 验证文件扩展名
        assert result.file_path.endswith('.pdf'), f"文件扩展名不正确: {result.file_path}"
        
        # 复制PDF文件到项目根目录供查看
        import shutil
        output_pdf_path = "ecs_diagnostic_report_test_output.pdf"
        shutil.copy2(result.file_path, output_pdf_path)
        print(f"\n✅ ECS诊断报告PDF已生成并复制到: {output_pdf_path}")
        print(f"📄 文件大小: {result.file_size:,} bytes")
        print(f"🎨 包含样式改进: 黑体标题、整体代码块、优化布局")

    def test_ecs_diagnostic_content_elements(self, pdf_service, temp_dir):
        """测试ECS诊断报告内容元素处理"""
        # Arrange - 包含各种markdown元素的测试内容
        test_content = """# 测试标题

## 二级标题

### 三级标题

#### 四级标题

这是包含 **粗体文本** 和 `行内代码` 的段落。

```bash
# 这是代码块
echo "测试代码块"
ls -la /proc/sysrq-trigger
```

```json
{
  "test": "JSON代码块",
  "value": 123
}
```

> 这是引用块内容

- 列表项目1
- 列表项目2
- 列表项目3

1. 有序列表1
2. 有序列表2
3. 有序列表3

[链接文本](https://example.com)

| 表格列1 | 表格列2 | 表格列3 |
|---------|---------|---------|
| 数据1   | 数据2   | 数据3   |
| 数据4   | 数据5   | 数据6   |
"""

        # Act
        result = pdf_service.generate_simple_report("content-elements-test", test_content)

        # Assert
        assert result.success, f"PDF生成失败: {result.error_message}"
        assert result.file_path is not None
        assert os.path.exists(result.file_path)
        file_size = os.path.getsize(result.file_path)
        assert file_size > 10000  # 确保内容被正确渲染
        
        # 复制内容元素测试PDF到项目根目录
        import shutil
        output_pdf_path = "content_elements_test_output.pdf"
        shutil.copy2(result.file_path, output_pdf_path)
        print(f"\n✅ 内容元素测试PDF已生成: {output_pdf_path}")
        print(f"📄 文件大小: {file_size:,} bytes")

    def test_ecs_diagnostic_markdown_formatting(self, pdf_service, temp_dir):
        """测试markdown格式化处理"""
        # Arrange - 测试各种markdown格式
        markdown_test_content = """# Markdown格式测试

## 文本格式测试

**粗体文本** 和 *斜体文本* 以及 ***粗斜体文本***

`行内代码` 和 ~~删除线文本~~

## 代码块测试

```python
def test_function():
    print("Hello, World!")
    return True
```

```bash
#!/bin/bash
echo "Shell脚本测试"
```

## 特殊字符测试

包含特殊字符: < > & " ' 

## 空格处理测试

这是一个包含    多个    空格的    段落。

## 链接测试

[阿里云官方文档](https://help.aliyun.com/zh/ecs/support/troubleshoot-instance-panic-issues)
"""

        # Act
        result = pdf_service.generate_simple_report("markdown-formatting-test", markdown_test_content)

        # Assert
        assert result.success, f"Markdown格式化测试失败: {result.error_message}"
        assert result.file_path is not None
        assert os.path.exists(result.file_path)
        
        # 验证文件不为空且有合理大小
        file_size = os.path.getsize(result.file_path)
        assert file_size > 5000, f"PDF文件大小过小，可能markdown处理有问题: {file_size} bytes"
        
        # 复制markdown格式测试PDF到项目根目录
        import shutil
        output_pdf_path = "markdown_formatting_test_output.pdf"
        shutil.copy2(result.file_path, output_pdf_path)
        print(f"\n✅ Markdown格式测试PDF已生成: {output_pdf_path}")
        print(f"📄 文件大小: {file_size:,} bytes")