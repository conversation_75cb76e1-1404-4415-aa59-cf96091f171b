
import pytest
from deep_diagnose.tools.mcp.mcp_tool_manager import MCPToolManager

@pytest.mark.asyncio
async def test_get_mcp_servers_integration():
    """Tests that get_mcp_servers returns a valid, non-empty dictionary."""
    manager = MCPToolManager()
    servers = await manager.get_enabled_mcp_tools()
    
    assert isinstance(servers, list)
    assert len(servers) > 0

@pytest.mark.asyncio
async def test_get_mcp_server_descriptions_integration():
    """Tests that get_tools_description returns a non-empty string with tool info."""
    mcp_settings = {}
    manager = MCPToolManager()

    descriptions = await manager.get_enabled_mcp_tools_description()

    print(descriptions)
    assert isinstance(descriptions, str)
    assert len(descriptions) > 0
    assert descriptions.startswith("## Available Tools")
    assert "Input Schema:" in descriptions
