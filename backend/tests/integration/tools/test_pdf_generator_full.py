import os
import tempfile

import pytest
from reportlab.platypus import Paragraph

from deep_diagnose.services.report_generator import create_pdf_generator, is_pdf_generation_available


class TestPDFGeneratorFullIntegration:
    """测试PDFGenerator类"""
    
    @pytest.fixture
    def temp_dir(self):
        """创建临时目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    @pytest.fixture
    def pdf_generator(self, temp_dir):
        """创建PDF生成器实例"""
        if not is_pdf_generation_available():
            pytest.skip("reportlab not available")
        return PDFGenerator(output_dir=temp_dir)
    

    def test_pdf_generator_initialization(self, temp_dir):
        """测试PDF生成器初始化"""
        # Arrange & Act
        generator = create_pdf_generator(output_dir=temp_dir)

        # Assert
        assert generator._service.config.output_dir == temp_dir
        assert hasattr(generator._service.config, 'title_style')
        assert hasattr(generator._service.config, 'heading_style')
        assert hasattr(generator._service.config, 'body_style')
        assert hasattr(generator._service.config, 'code_style')
        assert hasattr(generator._service.config, 'subheading_style')
    
    def test_generate_diagnostic_report(self, pdf_generator, temp_dir):
        """测试生成诊断报告PDF"""
        # Arrange
        task_id = "test-task-123"
        agent = "DiagnoseAgent"
        question = "测试问题：分析系统性能"
        result = """## 诊断结果\n\n### 问题概述\n发现系统存在性能瓶颈。\n\n### 主要发现\n- CPU使用率过高\n- 内存不足\n- 磁盘IO异常\n\n### 建议措施\n1. 优化CPU密集型任务\n2. 增加内存容量\n3. 检查磁盘健康状态\n"""
        detail = """## 执行过程\n\n### 步骤 1: Coordinator\n开始协调诊断任务\n\n### 步骤 2: Planner\n制定详细的诊断计划\n\n### 步骤 3: Researcher\n收集系统性能数据\n\n### 步骤 4: Reporter\n生成最终诊断报告\n\n### 执行统计\n- 总事件数: 15\n- 消息块数: 8\n- 工具调用数: 3\n"""
        submitted_at = "2023-10-27T10:00:00Z"
        completed_at = "2023-10-27T10:05:00Z"

        # Act
        pdf_path = pdf_generator.generate_diagnostic_report(
            task_id=task_id,
            agent=agent,
            question=question,
            result=result,
            detail=detail,
            submitted_at=submitted_at,
            completed_at=completed_at
        )

        # Assert
        assert pdf_path is not None
        
        # 检查是否是OSS URL还是本地路径
        if pdf_path.startswith('http'):
            # OSS URL
            assert "oss-cn-hangzhou.aliyuncs.com" in pdf_path
            # URL编码后的文件名
            assert "diagnostic_report_test-task-123" in pdf_path or "diagnostic-report_test-task-123" in pdf_path
            assert ".pdf" in pdf_path
        else:
            # 本地路径
            assert pdf_path.endswith(".pdf")
            assert "diagnostic_report_test-task-123" in pdf_path
        
    
    @pytest.mark.parametrize("markdown_content, expected_len_min, expected_types", [
        ("# Title\n\nParagraph", 2, [Paragraph]),
        ("", 1, [Paragraph]),  # Empty content should result in at least one element (e.g., empty paragraph)
        (None, 1, [Paragraph])  # None content should also result in at least one element
    ])
    def test_process_markdown_content_variations(self, pdf_generator, markdown_content, expected_len_min, expected_types):
        """测试处理Markdown内容的不同场景"""
        # Arrange is handled by parametrize

        # Act
        elements = pdf_generator._process_markdown_content(markdown_content)

        # Assert
        assert isinstance(elements, list)
        assert len(elements) >= expected_len_min

        # 验证包含预期类型的元素
        for expected_type in expected_types:
            assert any(isinstance(elem, expected_type) for elem in elements)

    def test_cleanup_old_files(self, pdf_generator, temp_dir):
        """测试清理旧文件"""
        # Arrange
        test_files = [
            "diagnostic_report_test1_20231027_100000.pdf",
            "diagnostic_report_test2_20231027_110000.pdf",
            "other_file.pdf"  # 不应该被清理
        ]

        for filename in test_files:
            filepath = os.path.join(temp_dir, filename)
            with open(filepath, 'w') as f:
                f.write("test content")

        # Act
        pdf_generator.cleanup_old_files(max_age_hours=0)

        # Assert
        assert not os.path.exists(os.path.join(temp_dir, test_files[0]))
        assert not os.path.exists(os.path.join(temp_dir, test_files[1]))
        assert os.path.exists(os.path.join(temp_dir, test_files[2]))
