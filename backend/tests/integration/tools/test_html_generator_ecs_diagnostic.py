import os
import tempfile

import pytest

from deep_diagnose.services.report_generator import create_html_generator, is_html_generation_available


class TestHTMLGeneratorECSDiagnostic:
    """测试HTML生成器 - ECS诊断报告样式"""
    
    @pytest.fixture
    def temp_dir(self):
        """创建临时目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    @pytest.fixture
    def html_service(self, temp_dir):
        """创建HTML生成器服务实例"""
        if not is_html_generation_available():
            pytest.skip("HTML generation not available")
        return HTMLGeneratorService(output_dir=temp_dir)
    
    def test_ecs_diagnostic_html_generation(self, html_service, temp_dir):
        """测试ECS诊断报告HTML生成 - 验证空格和缩进修复效果"""
        # Arrange - 真实的ECS诊断报告内容，包含您提到的问题场景
        ecs_diagnostic_content = """# i-bp131jfclul2mokez67x 实例重启深度诊断报告

## 诊断信息
实例ID: i-bp131jfclul2mokez67x  
时间范围: 2025-05-20 00:00:00 至 2025-05-22 23:59:59  
问题描述: 实例发生非预期重启事件

## 关键要点

通过分析 **get_vm_coredump** 工具输出，确认崩溃原因为用户执行 **sysrq-c** 或写入 **/proc/sysrq-trigger** 文件触发的内核 **panic** 异常。异常发生时间为 **2025-05-21T19:25:54+08:00**。  
**listMonitorExceptions** 工具检测到 **2** 次 "VM内部重启" 异常事件，发生时间为 **2025-05-21 19:25:03**。  
控制台操作日志（通过 **listActionTrail**）和客户侧运维事件（通过 **listReportedOperationalEvents**）均未发现任何平台侧发起的重启或通知。

## 推断过程

### 1. NC节点异常核查
通过 **listMonitorExceptions** 查询 NC 节点 (IP: ****************) 监控异常事件，检测到 **11** 次 "VM内部重启" 异常事件，最早发生于 **2025-05-20 04:30:03**，最晚发生于 **2025-05-22 20:06:59**。

	通过 listOperationRecords 和 listChangeRecords 查询 NC 节点运维规则命中记录和变更记录，结果均为空。
	
	确认 NC 节点层面没有主动发起导致实例重启的运维操作或配置变更。

### 2. 实例层面操作核查
通过 **listActionTrail** 查询控制台操作日志，检测到 `RunCommand`、`CreateNetworkInterface`、`CreateDisk`、`RunInstances` 等操作，但未发现 `RebootInstance` 等重启相关操作。

通过 **listReportedOperationalEvents** 查询客户侧运维事件，结果为空。

排除平台侧主动发起的重启操作可能性。

### 3. 内核崩溃日志分析
通过 **get_vm_coredump** 分析，检测到 `GuestOS.Panic.SysrqTrigger` 异常，确认是由用户主动触发的 **sysrq** 导致的内核 **panic**。

异常时间戳为 **2025-05-21T19:25:54+08:00**，与 **listMonitorExceptions** 检测到的 VM内部重启事件时间高度吻合。

	系统调用栈信息明确指向用户主动触发的 **sysrq** 操作。

### 4. 平台诊断工具验证
**runDiagnose** 工具诊断结果显示 **PANIC异常**，异常发生时间为 **2025-05-21 19:26:00**，异常原因代码为 `vm_kernel:panic:fatal`。

结合 **coredump** 分析结果，确认异常根本原因为虚拟机操作系统层面的用户操作导致。

## 总结及建议

综合各项诊断结果：

1）排除了 NC 节点故障、平台运维操作等外部因素；

2）确认从系统日志中识别出用户主动触发的 **sysrq-c** 或 **/proc/sysrq-trigger** 操作导致内核 **panic**；

3）核心证据包括：**get_vm_coredump** 的调用栈分析、**runDiagnose** 的异常诊断、**listMonitorExceptions** 的重启事件记录。

### 建议进一步检查以下方面：

	1）排查用户自身的运维操作日志，确认是否有误操作或自动化脚本意外触发此类行为；

	2）审查系统权限管理策略，限制对 **/proc/sysrq-trigger** 文件的访问权限；

	3）考虑禁用不必要的 **sysrq** 功能，或在生产环境中设置适当的 **sysrq** 触发保护机制；

	4）加强系统监控，对异常的内核 **panic** 事件进行实时告警。

如该行为非预期，请加强权限管理和脚本审计以防止类似情况再次发生。可参考 [阿里云官方文档](https://help.aliyun.com/zh/ecs/support/troubleshoot-instance-panic-issues) 进一步了解和预防类似情况。
"""

        detail_content = """# 详细诊断执行过程

## 工具执行记录

### 监控异常事件查询

#### listMonitorExceptions 执行结果

```json
{
  "RequestId": "A1B2C3D4-E5F6-7890-ABCD-EF1234567890",
  "TotalCount": 11,
  "MonitorExceptions": [
    {
      "ExceptionId": "me-bp1234567890abcdef",
      "ExceptionType": "VM内部重启",
      "ExceptionTime": "2025-05-21T19:25:03+08:00",
      "InstanceId": "i-bp131jfclul2mokez67x",
      "NodeIp": "************",
      "ExceptionLevel": "Critical"
    }
  ]
}
```

### 操作记录查询

#### listActionTrail 执行结果

```json
{
  "RequestId": "B2C3D4E5-F6G7-8901-BCDE-F23456789012",
  "TotalCount": 15,
  "ActionTrails": [
    {
      "EventTime": "2025-05-21T18:30:15+08:00",
      "EventName": "RunCommand",
      "UserName": "<EMAIL>",
      "SourceIpAddress": "*************",
      "UserAgent": "AlibabaCloud-Console"
    }
  ]
}
```

### 核心转储分析

#### get_vm_coredump 分析结果

```
Crash Analysis Report
=====================

Crash Time: 2025-05-21T19:25:54+08:00
Crash Type: GuestOS.Panic.SysrqTrigger
Kernel Version: 3.10.0-1160.el7.x86_64

Exception Details:
- Trigger: Manual sysrq-c command
- Process: bash (PID: 12345)
- User: root (UID: 0)
- Working Directory: /root

Call Stack:
[<ffffffff81063930>] sysrq_handle_crash+0x10/0x20
[<ffffffff81063d5a>] __handle_sysrq+0x12a/0x1a0
[<ffffffff81063e47>] write_sysrq_trigger+0x37/0x40
[<ffffffff811f2f88>] proc_reg_write+0x68/0xa0
[<ffffffff8118856a>] vfs_write+0xba/0x1e0
[<ffffffff81188924>] sys_write+0x54/0xa0
[<ffffffff8100b072>] system_call_fastpath+0x16/0x1b
```

### 系统诊断工具验证

#### runDiagnose 执行结果

```xml
<?xml version="1.0" encoding="UTF-8"?>
<DiagnoseResult>
  <RequestId>C3D4E5F6-G7H8-9012-CDEF-************</RequestId>
  <InstanceId>i-bp131jfclul2mokez67x</InstanceId>
  <DiagnoseTime>2025-05-22T10:30:00+08:00</DiagnoseTime>
  <Status>Completed</Status>
  <Results>
    <Exception>
      <Type>PANIC</Type>
      <Time>2025-05-21T19:26:00+08:00</Time>
      <Code>vm_kernel:panic:fatal</Code>
      <Description>Kernel panic triggered by sysrq</Description>
      <Severity>Critical</Severity>
    </Exception>
  </Results>
</DiagnoseResult>
```

## 预防措施

### 系统配置加固

```bash
#!/bin/bash
# 系统安全加固脚本

# 禁用sysrq功能
echo "kernel.sysrq = 0" >> /etc/sysctl.conf
sysctl -p

# 限制/proc/sysrq-trigger访问权限
chmod 600 /proc/sysrq-trigger
chown root:root /proc/sysrq-trigger

# 添加审计规则
echo "-w /proc/sysrq-trigger -p wa -k sysrq_access" >> /etc/audit/rules.d/sysrq.rules
service auditd restart
```

> **重要提示**: 在生产环境中，建议将sysrq功能完全禁用，以防止意外触发系统崩溃。
"""

        task_id = "ecs-diagnostic-test"
        agent = "ECS诊断代理"
        question = "ECS实例重启深度诊断 - 测试HTML样式改进效果"
        submitted_at = "2025-05-22T10:00:00Z"
        completed_at = "2025-05-22T12:00:00Z"

        # Act - 生成HTML报告
        result = html_service.generate_diagnostic_report(
            task_id=task_id,
            agent=agent,
            question=question,
            result=ecs_diagnostic_content,
            detail=detail_content,
            submitted_at=submitted_at,
            completed_at=completed_at
        )

        # Assert - 验证HTML生成结果
        assert result.success, f"HTML生成失败: {result.error_message}"
        assert result.file_path is not None, "HTML文件路径不应为空"
        assert result.file_size is not None, "HTML文件大小不应为空"
        assert result.file_size > 0, "HTML文件大小应大于0"
        
        # 验证文件确实存在
        assert os.path.exists(result.file_path), f"HTML文件不存在: {result.file_path}"
        
        # 验证文件大小合理
        actual_size = os.path.getsize(result.file_path)
        assert actual_size > 10000, f"HTML文件大小过小: {actual_size} bytes，可能内容渲染不完整"
        assert actual_size == result.file_size, "返回的文件大小与实际文件大小不匹配"
        
        # 验证文件名格式
        expected_filename_pattern = f"diagnostic_report_{task_id}_"
        assert expected_filename_pattern in os.path.basename(result.file_path), \
            f"文件名格式不正确: {os.path.basename(result.file_path)}"
        
        # 验证文件扩展名
        assert result.file_path.endswith('.html'), f"文件扩展名不正确: {result.file_path}"
        
        # 验证HTML内容
        with open(result.file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 检查关键内容
        assert 'get_vm_coredump' in html_content, "HTML中应包含get_vm_coredump"
        assert 'GuestOS.Panic.SysrqTrigger' in html_content, "HTML中应包含GuestOS.Panic.SysrqTrigger"
        assert 'class="indent"' in html_content, "HTML中应包含缩进样式"
        assert '<code>GuestOS.Panic.SysrqTrigger</code>' in html_content, "行内代码应正确渲染"
        assert '云计算运维诊断报告' in html_content, "HTML中应包含报告标题"
        
        # 复制HTML文件到项目根目录供查看
        import shutil
        output_html_path = "ecs_diagnostic_report_test_output.html"
        shutil.copy2(result.file_path, output_html_path)
        print(f"\n✅ ECS诊断报告HTML已生成并复制到: {output_html_path}")
        print(f"📄 文件大小: {result.file_size:,} bytes")
        print(f"🎨 包含修复: 精确空格控制、正确缩进显示、行内代码渲染")
        print(f"🔍 请在浏览器中打开查看效果")

    def test_ecs_diagnostic_simple_report(self, html_service, temp_dir):
        """测试简单HTML报告生成"""
        # Arrange - 包含问题场景的测试内容
        test_content = """# 测试报告

## 问题验证

### 空格问题测试
通过 **get_vm_coredump** 分析，检测到 `GuestOS.Panic.SysrqTrigger` 异常，确认是由用户主动触发的 **sysrq** 导致的内核 **panic**。

### 缩进问题测试

通过 **listMonitorExceptions** 查询监控异常事件。

	通过 listOperationRecords 查询运维记录，结果均为空。
	
	确认没有主动发起的运维操作。

### 代码块测试

```bash
echo "测试代码块"
ls -la /proc/sysrq-trigger
```

### 表格测试

| 工具名称 | 执行结果 | 状态 |
|---------|---------|------|
| get_vm_coredump | 检测到异常 | 成功 |
| listMonitorExceptions | 11次重启事件 | 成功 |

> 这是引用块测试
"""

        # Act
        result = html_service.generate_simple_report("simple-test", test_content)

        # Assert
        assert result.success, f"HTML生成失败: {result.error_message}"
        assert result.file_path is not None
        assert os.path.exists(result.file_path)
        file_size = os.path.getsize(result.file_path)
        assert file_size > 5000  # 确保内容被正确渲染
        
        # 验证HTML内容
        with open(result.file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 检查关键修复
        assert '<code>GuestOS.Panic.SysrqTrigger</code>' in html_content, "行内代码应正确渲染"
        assert 'class="indent"' in html_content, "缩进样式应存在"
        assert '<table>' in html_content, "表格应正确渲染"
        assert '<blockquote>' in html_content, "引用块应正确渲染"
        
        # 复制简单报告HTML到项目根目录
        import shutil
        output_html_path = "simple_report_test_output.html"
        shutil.copy2(result.file_path, output_html_path)
        print(f"\n✅ 简单报告HTML已生成: {output_html_path}")
        print(f"📄 文件大小: {file_size:,} bytes")