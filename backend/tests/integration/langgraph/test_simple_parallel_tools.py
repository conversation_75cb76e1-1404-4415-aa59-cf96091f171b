#!/usr/bin/env python3
"""
简化的并发工具执行测试

直接测试 create_react_agent 是否能够并发调用多个工具
"""

import asyncio
import sys
import os
from typing import List, Dict, Any

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../src'))

from langchain_core.messages import HumanMessage
from langchain_core.tools import tool
from langgraph.prebuilt import create_react_agent


class MockLLM:
    """模拟支持并发工具调用的 LLM"""
    
    def __init__(self):
        self.model_kwargs = {"parallel_tool_calls": True}
        self.bound_tools = []
    
    async def ainvoke(self, messages, **kwargs):
        """模拟 LLM 返回多个工具调用"""
        from langchain_core.messages import AIMessage
        
        # 模拟 LLM 决定并发调用多个工具
        return AIMessage(
            content="我将并发调用多个工具来诊断这些ECS实例的问题。",
            tool_calls=[
                {
                    "id": "call_1",
                    "name": "get_vm_basic_info",
                    "args": {"instance_id": "i-t4n4vky24zw2w1qnqoyf"}
                },
                {
                    "id": "call_2", 
                    "name": "list_vm_host_history",
                    "args": {
                        "instance_id": "i-t4n4vky24zw2w1qnqoyf",
                        "start_time": "2025-06-26 01:00:00",
                        "end_time": "2025-06-26 04:00:00"
                    }
                },
                {
                    "id": "call_3",
                    "name": "list_operational_events",
                    "args": {
                        "instance_id": "i-t4n4vky24zw2w1qnqoyf",
                        "start_time": "2025-06-26 01:00:00", 
                        "end_time": "2025-06-26 04:00:00"
                    }
                }
            ]
        )
    
    def bind(self, **kwargs):
        """支持 bind 方法"""
        new_llm = MockLLM()
        new_llm.model_kwargs.update(kwargs)
        new_llm.bound_tools = self.bound_tools.copy()
        return new_llm
    
    def bind_tools(self, tools, **kwargs):
        """支持 bind_tools 方法"""
        new_llm = MockLLM()
        new_llm.model_kwargs.update(self.model_kwargs)
        new_llm.model_kwargs.update(kwargs)
        new_llm.bound_tools = tools
        return new_llm


# 定义模拟工具
@tool
async def get_vm_basic_info(instance_id: str) -> Dict[str, Any]:
    """获取虚拟机基础信息"""
    await asyncio.sleep(0.5)  # 模拟网络延迟
    return {
        "instance_id": instance_id,
        "status": "Stopped",
        "create_time": "2025-06-25T10:00:00Z",
        "region": "cn-hangzhou",
        "tool_execution_time": "0.5s"
    }


@tool
async def list_vm_host_history(instance_id: str, start_time: str, end_time: str) -> Dict[str, Any]:
    """查询虚拟机宿主机历史"""
    await asyncio.sleep(0.7)  # 模拟网络延迟
    return {
        "instance_id": instance_id,
        "host_history": [
            {
                "host_id": "host-123",
                "start_time": start_time,
                "end_time": end_time,
                "event": "migration"
            }
        ],
        "tool_execution_time": "0.7s"
    }


@tool
async def list_operational_events(instance_id: str, start_time: str, end_time: str) -> Dict[str, Any]:
    """查询运维事件"""
    await asyncio.sleep(0.6)  # 模拟网络延迟
    return {
        "instance_id": instance_id,
        "events": [
            {
                "event_type": "SystemMaintenance",
                "start_time": "2025-06-26T01:30:00Z",
                "end_time": "2025-06-26T03:30:00Z"
            }
        ],
        "tool_execution_time": "0.6s"
    }


async def test_parallel_tools_execution():
    """测试并发工具执行"""
    print("=== 测试并发工具执行 ===")
    
    # 创建模拟 LLM
    llm = MockLLM()
    
    # 确保启用并发工具调用
    llm = llm.bind(parallel_tool_calls=True)
    print(f"LLM 配置: {llm.model_kwargs}")
    
    # 创建工具列表
    tools = [
        get_vm_basic_info,
        list_vm_host_history,
        list_operational_events
    ]
    
    # 使用 create_react_agent 创建 agent
    agent = create_react_agent(
        name="researcher",
        model=llm,
        tools=tools,
        prompt="你是一个ECS诊断专家，请并发调用多个工具来诊断实例问题。"
    )
    
    # 准备输入
    test_question = """这些实例在2025年6月26日凌晨1-4点发生了不可用，请排查问题的原因。
    (i-t4n4vky24zw2w1qnqoyf, i-t4n74bsfzx58x0lj4qbh, i-t4na3cc0c9mimcw9667x, i-j6ch2zf4qfy1rltbql6r, i-2vc5alcmxz75rw8aol4g, i-2vc0zliaw8ilg744cdrq, i-2vc6qv34j96hkmcwms5d)
    
    请同时调用以下工具进行诊断：
    1. get_vm_basic_info - 获取实例基础信息
    2. list_vm_host_history - 查询宿主机迁移历史  
    3. list_operational_events - 查询运维事件
    """
    
    agent_input = {
        "messages": [HumanMessage(content=test_question)]
    }
    
    # 执行 agent 并计时
    print("开始执行 agent...")
    start_time = asyncio.get_event_loop().time()
    
    try:
        result = await agent.ainvoke(
            input=agent_input,
            config={"recursion_limit": 10}
        )
        
        end_time = asyncio.get_event_loop().time()
        execution_time = end_time - start_time
        
        print(f"✓ Agent 执行完成，耗时: {execution_time:.2f}秒")
        
        # 分析结果
        if "messages" in result:
            messages = result["messages"]
            print(f"✓ 返回了 {len(messages)} 条消息")
            
            # 查找包含工具调用的消息
            for i, msg in enumerate(messages):
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    print(f"✓ 消息 {i} 包含 {len(msg.tool_calls)} 个工具调用:")
                    for j, tool_call in enumerate(msg.tool_calls):
                        print(f"  - 工具 {j+1}: {tool_call.get('name', 'unknown')}")
                
                if hasattr(msg, 'content') and msg.content:
                    print(f"消息 {i} 内容: {msg.content[:100]}...")
        
        # 验证并发执行效果
        if execution_time < 1.5:  # 如果是并发执行，应该比串行执行(0.5+0.7+0.6=1.8s)快
            print("✓ 执行时间表明可能实现了并发执行")
        else:
            print("⚠ 执行时间较长，可能未实现真正的并发")
            
        return True
        
    except Exception as e:
        print(f"✗ Agent 执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_tools_individually():
    """单独测试每个工具的执行时间"""
    print("\n=== 测试单个工具执行时间 ===")
    
    tools_to_test = [
        (get_vm_basic_info, {"instance_id": "i-test"}),
        (list_vm_host_history, {
            "instance_id": "i-test",
            "start_time": "2025-06-26 01:00:00",
            "end_time": "2025-06-26 04:00:00"
        }),
        (list_operational_events, {
            "instance_id": "i-test", 
            "start_time": "2025-06-26 01:00:00",
            "end_time": "2025-06-26 04:00:00"
        })
    ]
    
    total_time = 0
    for tool_func, args in tools_to_test:
        start_time = asyncio.get_event_loop().time()
        result = await tool_func.ainvoke(args)
        end_time = asyncio.get_event_loop().time()
        
        execution_time = end_time - start_time
        total_time += execution_time
        
        print(f"✓ {tool_func.name}: {execution_time:.2f}秒")
    
    print(f"串行执行总时间: {total_time:.2f}秒")
    return total_time


async def main():
    """主测试函数"""
    print("开始并发工具执行测试\n")
    
    # 先测试单个工具的执行时间
    serial_time = await test_tools_individually()
    
    # 再测试并发执行
    success = await test_parallel_tools_execution()
    
    print(f"\n=== 测试总结 ===")
    print(f"串行执行预期时间: {serial_time:.2f}秒")
    print(f"测试结果: {'成功' if success else '失败'}")
    
    if success:
        print("✓ 并发工具调用测试通过")
        print("✓ create_react_agent 支持多工具并发执行")
    else:
        print("✗ 并发工具调用测试失败")
    
    return success


if __name__ == "__main__":
    # 运行测试
    result = asyncio.run(main())
    exit(0 if result else 1)