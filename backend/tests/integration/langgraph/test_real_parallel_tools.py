#!/usr/bin/env python3
"""
使用真实 LLM 测试并发工具执行

基于项目实际配置，测试 create_react_agent 的并发工具调用能力
"""

import asyncio
import sys
import os
from typing import List, Dict, Any
from unittest.mock import patch, AsyncMock

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../src'))

from langchain_core.messages import HumanMessage
from langchain_core.tools import tool
from langchain_core.runnables import RunnableConfig
from langgraph.prebuilt import create_react_agent

# 导入项目模块
from deep_diagnose.core.reasoning.agents.utils import create_agent_with_tools, setup_mcp_tools
from deep_diagnose.llms.llm import get_llm_by_type
from deep_diagnose.common.config.constants.agents import AGENT_LLM_MAP


# 定义模拟的 MCP 工具
@tool
async def getVmBasicInfo(instanceId: str) -> Dict[str, Any]:
    """获取虚拟机基础信息"""
    print(f"🔧 执行工具: getVmBasicInfo({instanceId})")
    await asyncio.sleep(0.5)  # 模拟网络延迟
    return {
        "instanceId": instanceId,
        "status": "Stopped",
        "createTime": "2025-06-25T10:00:00Z",
        "region": "cn-hangzhou",
        "executionTime": "0.5s"
    }


@tool
async def listVmHostHistory(instanceId: str, startTime: str, endTime: str) -> Dict[str, Any]:
    """查询虚拟机宿主机历史"""
    print(f"🔧 执行工具: listVmHostHistory({instanceId}, {startTime}, {endTime})")
    await asyncio.sleep(0.7)  # 模拟网络延迟
    return {
        "instanceId": instanceId,
        "hostHistory": [
            {
                "hostId": "host-123",
                "startTime": startTime,
                "endTime": endTime,
                "event": "migration"
            }
        ],
        "executionTime": "0.7s"
    }


@tool
async def listReportedOperationalEvents(instanceId: str, startTime: str, endTime: str) -> Dict[str, Any]:
    """查询运维事件"""
    print(f"🔧 执行工具: listReportedOperationalEvents({instanceId}, {startTime}, {endTime})")
    await asyncio.sleep(0.6)  # 模拟网络延迟
    return {
        "instanceId": instanceId,
        "events": [
            {
                "eventType": "SystemMaintenance",
                "startTime": "2025-06-26T01:30:00Z",
                "endTime": "2025-06-26T03:30:00Z"
            }
        ],
        "executionTime": "0.6s"
    }


async def test_with_real_llm_and_create_agent_with_tools():
    """使用真实 LLM 和 create_agent_with_tools 函数测试"""
    print("=== 使用真实 LLM 测试并发工具执行 ===")
    
    # 创建模拟配置
    mock_config = RunnableConfig(
        configurable={
            "mcp_settings": {
                "servers": {
                    "diagnose": {
                        "enabled_tools": [
                            "getVmBasicInfo",
                            "listVmHostHistory", 
                            "listReportedOperationalEvents"
                        ],
                        "add_to_agents": ["researcher"]
                    }
                }
            }
        }
    )
    
    # 模拟 setup_mcp_tools 返回我们的测试工具
    mock_tools = [getVmBasicInfo, listVmHostHistory, listReportedOperationalEvents]
    
    with patch('deep_diagnose.core.reasoning.agents.utils.setup_mcp_tools', return_value=mock_tools):
        try:
            # 使用项目的 create_agent_with_tools 函数
            print("创建 agent...")
            agent = await create_agent_with_tools("researcher", mock_tools, mock_config)
            print("✓ Agent 创建成功")
            
            # 准备测试输入
            test_question = """这些实例在2025年6月26日凌晨1-4点发生了不可用，请排查问题的原因。
            (i-t4n4vky24zw2w1qnqoyf, i-t4n74bsfzx58x0lj4qbh, i-t4na3cc0c9mimcw9667x, i-j6ch2zf4qfy1rltbql6r, i-2vc5alcmxz75rw8aol4g, i-2vc0zliaw8ilg744cdrq, i-2vc6qv34j96hkmcwms5d)
            
            请同时调用多个工具进行并发诊断：
            1. getVmBasicInfo - 获取第一个实例的基础信息
            2. listVmHostHistory - 查询第一个实例的宿主机迁移历史  
            3. listReportedOperationalEvents - 查询第一个实例的运维事件
            
            请一次性调用这三个工具来提高诊断效率。
            """
            
            agent_input = {
                "messages": [HumanMessage(content=test_question)]
            }
            
            # 执行 agent 并计时
            print("开始执行 agent...")
            start_time = asyncio.get_event_loop().time()
            
            result = await agent.ainvoke(
                input=agent_input,
                config={"recursion_limit": 10}
            )
            
            end_time = asyncio.get_event_loop().time()
            execution_time = end_time - start_time
            
            print(f"✓ Agent 执行完成，总耗时: {execution_time:.2f}秒")
            
            # 分析结果
            if "messages" in result:
                messages = result["messages"]
                print(f"✓ 返回了 {len(messages)} 条消息")
                
                # 查找工具调用
                tool_calls_found = 0
                for i, msg in enumerate(messages):
                    if hasattr(msg, 'tool_calls') and msg.tool_calls:
                        tool_calls_found += len(msg.tool_calls)
                        print(f"✓ 消息 {i} 包含 {len(msg.tool_calls)} 个工具调用:")
                        for j, tool_call in enumerate(msg.tool_calls):
                            tool_name = tool_call.get('name', 'unknown')
                            print(f"  - 工具 {j+1}: {tool_name}")
                    
                    if hasattr(msg, 'content') and msg.content and len(msg.content) > 10:
                        content_preview = msg.content[:150] + "..." if len(msg.content) > 150 else msg.content
                        print(f"消息 {i} 内容: {content_preview}")
                
                print(f"✓ 总共发现 {tool_calls_found} 个工具调用")
                
                # 评估并发效果
                if tool_calls_found >= 3:
                    print("✓ 检测到多个工具调用，可能实现了并发执行")
                    if execution_time < 2.0:  # 串行执行需要 0.5+0.7+0.6=1.8秒
                        print("✓ 执行时间表明实现了并发执行")
                    else:
                        print("⚠ 执行时间较长，可能是串行执行")
                else:
                    print("⚠ 工具调用数量较少，可能未实现预期的并发")
            
            return True
            
        except Exception as e:
            print(f"✗ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False


async def test_direct_create_react_agent():
    """直接使用 create_react_agent 测试"""
    print("\n=== 直接使用 create_react_agent 测试 ===")
    
    try:
        # 获取真实的 LLM
        llm = get_llm_by_type("reasoning")
        print(f"✓ 获取 LLM: {type(llm).__name__}")
        
        # 确保启用并发工具调用
        if hasattr(llm, 'bind'):
            llm = llm.bind(parallel_tool_calls=True)
            print("✓ 启用并发工具调用")
        
        # 创建工具列表
        tools = [getVmBasicInfo, listVmHostHistory, listReportedOperationalEvents]
        
        # 创建 agent
        agent = create_react_agent(
            name="researcher",
            model=llm,
            tools=tools,
            prompt="你是一个ECS诊断专家。当用户要求诊断多个实例问题时，请同时调用多个工具来提高效率。"
        )
        print("✓ 直接创建 react agent 成功")
        
        # 准备输入
        agent_input = {
            "messages": [
                HumanMessage(
                    content="""请同时调用以下三个工具来诊断实例 i-t4n4vky24zw2w1qnqoyf 在2025年6月26日凌晨1-4点的不可用问题：

1. getVmBasicInfo(instanceId="i-t4n4vky24zw2w1qnqoyf")
2. listVmHostHistory(instanceId="i-t4n4vky24zw2w1qnqoyf", startTime="2025-06-26 01:00:00", endTime="2025-06-26 04:00:00")  
3. listReportedOperationalEvents(instanceId="i-t4n4vky24zw2w1qnqoyf", startTime="2025-06-26 01:00:00", endTime="2025-06-26 04:00:00")

请一次性调用这三个工具，不要分步骤执行。"""
                )
            ]
        }
        
        # 执行
        print("开始执行...")
        start_time = asyncio.get_event_loop().time()
        
        result = await agent.ainvoke(
            input=agent_input,
            config={"recursion_limit": 10}
        )
        
        end_time = asyncio.get_event_loop().time()
        execution_time = end_time - start_time
        
        print(f"✓ 执行完成，耗时: {execution_time:.2f}秒")
        
        # 分析结果
        if "messages" in result:
            messages = result["messages"]
            print(f"✓ 返回 {len(messages)} 条消息")
            
            for i, msg in enumerate(messages):
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    print(f"✓ 消息 {i} 包含 {len(msg.tool_calls)} 个工具调用")
        
        return True
        
    except Exception as e:
        print(f"✗ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("开始并发工具执行测试\n")
    
    # 测试1: 使用项目的 create_agent_with_tools
    success1 = await test_with_real_llm_and_create_agent_with_tools()
    
    # 测试2: 直接使用 create_react_agent
    success2 = await test_direct_create_react_agent()
    
    print(f"\n=== 测试总结 ===")
    print(f"create_agent_with_tools 测试: {'✓ 成功' if success1 else '✗ 失败'}")
    print(f"create_react_agent 测试: {'✓ 成功' if success2 else '✗ 失败'}")
    
    if success1 or success2:
        print("✓ 至少一个测试通过，并发工具调用功能可用")
    else:
        print("✗ 所有测试失败，需要进一步调试")
    
    return success1 or success2


if __name__ == "__main__":
    # 运行测试
    result = asyncio.run(main())
    exit(0 if result else 1)