#!/usr/bin/env python3
"""
最终的并发工具执行测试

验证修复后的 step_executor.py 能够输出多个工具调用
"""

import asyncio
import sys
import os
import pytest
from typing import Dict, Any

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../src'))

from langchain_core.messages import HumanMessage
from langchain_core.tools import tool
from langchain_core.runnables import RunnableConfig

# 导入项目模块
from deep_diagnose.core.reasoning.agents.utils import create_agent_with_tools


# 定义测试工具
@tool
async def getVmBasicInfo(instanceId: str) -> Dict[str, Any]:
    """获取虚拟机基础信息"""
    print(f"🔧 [并发执行] getVmBasicInfo({instanceId})")
    await asyncio.sleep(0.1)  # 模拟执行时间
    return {
        "instanceId": instanceId,
        "status": "Stopped",
        "region": "cn-hangzhou"
    }


@tool  
async def listVmHostHistory(instanceId: str, startTime: str, endTime: str) -> Dict[str, Any]:
    """查询虚拟机宿主机历史"""
    print(f"🔧 [并发执行] listVmHostHistory({instanceId})")
    await asyncio.sleep(0.1)  # 模拟执行时间
    return {
        "instanceId": instanceId,
        "hostHistory": [{"event": "migration"}]
    }


@tool
async def listReportedOperationalEvents(instanceId: str, startTime: str, endTime: str) -> Dict[str, Any]:
    """查询运维事件"""
    print(f"🔧 [并发执行] listReportedOperationalEvents({instanceId})")
    await asyncio.sleep(0.1)  # 模拟执行时间
    return {
        "instanceId": instanceId,
        "events": [{"eventType": "SystemMaintenance"}]
    }


class TestConcurrentToolsExecution:
    """测试并发工具执行"""
    
    @pytest.mark.asyncio
    async def test_step_executor_parallel_tools(self):
        """测试 step_executor 风格的并发工具调用"""
        print("\n=== 测试 step_executor 并发工具调用 ===")
        
        # 创建模拟配置
        config = RunnableConfig(
            configurable={
                "mcp_settings": {
                    "servers": {
                        "diagnose": {
                            "enabled_tools": ["getVmBasicInfo", "listVmHostHistory", "listReportedOperationalEvents"],
                            "add_to_agents": ["researcher"]
                        }
                    }
                }
            }
        )
        
        # 准备工具
        tools = [getVmBasicInfo, listVmHostHistory, listReportedOperationalEvents]
        
        # 使用项目的 create_agent_with_tools 函数
        with pytest.MonkeyPatch().context() as m:
            # Mock setup_mcp_tools 返回我们的测试工具
            m.setattr('deep_diagnose.core.reasoning.agents.utils.setup_mcp_tools', 
                     lambda agent_type, config: tools)
            
            # 创建 agent
            agent = await create_agent_with_tools("researcher", tools, config)
            
            # 准备输入 - 模拟 step_executor 的输入格式
            test_instances = [
                "i-t4n4vky24zw2w1qnqoyf", "i-t4n74bsfzx58x0lj4qbh", 
                "i-t4na3cc0c9mimcw9667x", "i-j6ch2zf4qfy1rltbql6r",
                "i-2vc5alcmxz75rw8aol4g", "i-2vc0zliaw8ilg744cdrq", 
                "i-2vc6qv34j96hkmcwms5d"
            ]
            
            agent_input = {
                "messages": [
                    HumanMessage(
                        content=f"""这些实例在2025年6月26日凌晨1-4点发生了不可用，请排查问题的原因。
                        ({', '.join(test_instances)})
                        
                        请同时调用以下工具进行并发诊断：
                        1. getVmBasicInfo - 获取实例基础信息
                        2. listVmHostHistory - 查询宿主机迁移历史
                        3. listReportedOperationalEvents - 查询运维事件
                        
                        请一次性调用这三个工具来提高诊断效率。
                        """
                    )
                ]
            }
            
            # 执行 agent
            print("开始执行 agent...")
            start_time = asyncio.get_event_loop().time()
            
            result = await agent.ainvoke(
                input=agent_input,
                config={"recursion_limit": 10}
            )
            
            end_time = asyncio.get_event_loop().time()
            execution_time = end_time - start_time
            
            print(f"✓ 执行完成，耗时: {execution_time:.2f}秒")
            
            # 验证结果
            assert result is not None
            assert "messages" in result
            
            # 分析工具调用
            tool_calls_count = 0
            tool_names = []
            
            for msg in result["messages"]:
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    tool_calls_count += len(msg.tool_calls)
                    for tool_call in msg.tool_calls:
                        tool_name = tool_call.get('name', 'unknown')
                        tool_names.append(tool_name)
                        print(f"✓ 检测到工具调用: {tool_name}")
            
            print(f"✓ 总工具调用数: {tool_calls_count}")
            print(f"✓ 工具列表: {tool_names}")
            
            # 验证并发执行
            assert tool_calls_count >= 2, f"期望至少2个工具调用，实际: {tool_calls_count}"
            
            # 验证包含期望的工具
            expected_tools = ["getVmBasicInfo", "listVmHostHistory", "listReportedOperationalEvents"]
            found_tools = [tool for tool in expected_tools if tool in tool_names]
            
            print(f"✓ 找到期望工具: {found_tools}")
            assert len(found_tools) >= 2, f"期望至少找到2个目标工具，实际: {len(found_tools)}"
            
            # 如果找到3个工具调用，说明实现了完全并发
            if tool_calls_count >= 3:
                print("🎉 成功实现3个工具的并发调用！")
            
            return True


def test_manual_execution():
    """手动执行测试（非 pytest）"""
    async def run_test():
        test_instance = TestConcurrentToolsExecution()
        try:
            await test_instance.test_step_executor_parallel_tools()
            print("\n🎉 测试成功！并发工具调用功能正常工作")
            return True
        except Exception as e:
            print(f"\n❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    return asyncio.run(run_test())


if __name__ == "__main__":
    # 直接运行测试
    print("开始并发工具调用测试...")
    success = test_manual_execution()
    
    if success:
        print("\n✅ 所有测试通过")
        print("✅ step_executor.py 现在支持输出多个工具调用")
        print("✅ 并发执行功能已实现")
    else:
        print("\n❌ 测试失败")
    
    exit(0 if success else 1)