import os
import json
from typing import TypedDict, Annotated, List, Dict
from langchain_core.messages import BaseMessage, HumanMessage
from langgraph.graph import StateGraph, END
from langchain_core.runnables import RunnableParallel
import operator

from deep_diagnose.llms.llm import get_llm_by_type


# --- 1. 环境设置 ---
# 使用ChatQwen替代OpenAI，通过配置文件管理模型参数

# --- 2. 定义图的状态 (Graph State) ---
# 状态是整个流程的核心，它会跟踪所有信息，并在节点之间传递。

class GraphState(TypedDict):
    """
    定义图的状态

    Attributes:
        initial_request (str): 用户的原始请求。
        plan (List[Dict]): LLM规划师生成的执行计划 (DAG)。
        executed_tasks (List[int]): 已执行完成的任务ID列表。
        tool_results (List[Dict]): 每个工具执行后的结果，包含任务ID和结果。
        tasks_to_execute (List[Dict]): 在当前步骤中将要被并行执行的任务。
        final_response (str): 最终生成的文本响应。
    """
    initial_request: str
    plan: List[Dict]
    # 使用 operator.add 可以让新旧列表合并，而不是替换
    executed_tasks: Annotated[List[int], operator.add]
    tool_results: Annotated[List[Dict], operator.add]
    tasks_to_execute: List[Dict]
    final_response: str


# --- 3. 创建工具 (Tools) ---
# 在实际应用中，这些可以是任意复杂的 LangChain Tool。

def search_tool(query: str) -> str:
    """模拟一个网络搜索工具。"""
    print(f"\n--- 正在执行搜索工具 ---")
    print(f"查询内容: {query}")
    # 实际应用中会调用真实的API, 例如 Tavily, Google Search等
    return f"关于'{query}'的搜索结果表明，最新的多模态模型（如GPT-4o和Gemini）能够同时理解和处理文本、图像和音频信息，实现了更强的交互能力。"


def write_code_tool(topic) -> str:
    """模拟一个代码编写工具。"""
    print(f"\n--- 正在执行代码编写工具 ---")
    print(f"代码主题: {topic}")
    
    # Handle both string and dict inputs
    if isinstance(topic, dict):
        topic_str = str(topic)
    else:
        topic_str = str(topic)
    
    return f"""
# Python代码示例：模拟一个简单的多模态模型概念
class MultiModalModel:
    def __init__(self):
        print("初始化多模态模型...")

    def process_input(self, text=None, image=None):
        if text and image:
            return f"处理文本 '{{text}}' 和一张图片，生成综合理解。"
        elif text:
            return f"只处理文本: '{{text}}'"
        elif image:
            return "只处理一张图片。"
        else:
            return "没有提供输入。"

# 基于 '{topic_str}' 的概念
model = MultiModalModel()
result = model.process_input(text="描述这张图片", image="cat.jpg")
print(result)
"""


# 工具注册表，将任务名称映射到具体的工具函数
# 注意：这里的键（如 "search", "write_code"）需要与Planner生成的任务名称对应
TOOL_REGISTRY = {
    "search": search_tool,
    "write_code": write_code_tool,
}


# --- 4. 定义图的节点 (Graph Nodes) ---

def planner_node(state: GraphState):
    """
    规划节点：接收用户请求，并使用LLM生成一个带依赖关系的执行计划 (DAG)。
    """
    print("\n>>> 进入规划节点 (Planner Node)")

    prompt = f"""
你是一个专业的任务规划师。根据用户的请求，将其分解为一系列具体的、可执行的步骤。
你需要输出一个 JSON 格式的列表，其中每个对象代表一个任务。
每个任务对象必须包含以下字段：
- "id": 任务的唯一整数标识，从1开始。
- "task": 对任务的具体描述，需要清晰地说明要调用什么工具以及相关参数。
- "tool": 要使用的工具名称（必须是 '{', '.join(TOOL_REGISTRY.keys())}' 中的一个）。
- "dependencies": 一个整数列表，包含当前任务所依赖的所有任务的 "id"。如果没有依赖，则为空列表。

请为以下用户请求制定计划：
请求: "{state['initial_request']}"
"""

    llm = get_llm_by_type("reasoning")
    response = llm.invoke(prompt)
    
    # Extract JSON from markdown code blocks if present
    content = response.content.strip()
    if content.startswith("```json") and content.endswith("```"):
        content = content[7:-3].strip()
    elif content.startswith("```") and content.endswith("```"):
        content = content[3:-3].strip()
    
    plan = json.loads(content)

    print("--- 生成的执行计划 ---")
    print(json.dumps(plan, indent=2, ensure_ascii=False))

    return {"plan": plan}


def dispatcher_node(state: GraphState):
    """
    调度节点：根据计划(DAG)和已完成的任务，找出下一步可以并行执行的所有任务。
    """
    print("\n>>> 进入调度节点 (Dispatcher Node)")

    plan = state["plan"]
    executed_tasks = state["executed_tasks"]

    # 找出所有依赖项都已满足的任务
    ready_tasks = []
    for task in plan:
        if task["id"] in executed_tasks:
            continue  # 跳过已完成的任务

        # 检查所有依赖是否都已完成
        all_dependencies_met = all(dep in executed_tasks for dep in task.get("dependencies", []))

        if all_dependencies_met:
            ready_tasks.append(task)

    print(f"--- 准备执行的任务 ID: {[task['id'] for task in ready_tasks]} ---")

    return {"tasks_to_execute": ready_tasks}


def tool_executor_node(state: GraphState):
    """
    工具执行节点：并行执行所有待处理的任务，并收集结果。
    """
    print("\n>>> 进入工具执行节点 (Tool Executor Node)")

    tasks_to_execute = state["tasks_to_execute"]
    if not tasks_to_execute:
        print("--- 没有需要执行的新任务 ---")
        return {}

    # 直接执行任务，不使用 RunnableParallel 来避免输入格式问题
    results = {}
    for task in tasks_to_execute:
        if task["tool"] in TOOL_REGISTRY:
            tool_func = TOOL_REGISTRY[task["tool"]]
            task_result = tool_func(task["task"])
            results[str(task["id"])] = task_result

    # 格式化结果并更新状态
    newly_executed_ids = [int(task_id) for task_id in results.keys()]
    formatted_results = [{"task_id": int(tid), "result": res} for tid, res in results.items()]

    print("--- 工具执行结果 ---")
    print(json.dumps(formatted_results, indent=2, ensure_ascii=False))

    return {
        "executed_tasks": newly_executed_ids,
        "tool_results": formatted_results
    }


def final_response_node(state: GraphState):
    """
    最终响应节点：所有任务完成后，综合所有结果，生成最终的答复。
    """
    print("\n>>> 进入最终响应节点 (Final Response Node)")

    prompt = f"""
你是一个总结专家。根据用户的原始请求和一系列工具的执行结果，生成一个全面、清晰的最终答复。

原始请求: {state['initial_request']}

工具执行结果:
{json.dumps(state['tool_results'], indent=2, ensure_ascii=False)}

请用中文生成最终的、整合的答复。
"""
    llm = get_llm_by_type("basic")
    response = llm.invoke(prompt)

    return {"final_response": response.content}


# --- 5. 定义条件边 (Conditional Edges) ---

def should_continue(state: GraphState):
    """
    判断是继续执行任务循环还是结束流程。
    """
    print("\n>>> 进入条件判断 (Should Continue?)")

    if len(state["executed_tasks"]) == len(state["plan"]):
        print("--- 所有任务完成，准备生成最终答复 ---")
        return "end_process"
    else:
        print("--- 仍有未完成的任务，继续调度 ---")
        return "continue_execution"


def dispatcher_check(state: GraphState):
    """
    检查调度器是否找到了可执行的任务
    """
    if state["tasks_to_execute"]:
        return "execute_tools"
    else:
        # 如果没有更多任务可以调度，但并非所有任务都已完成，说明计划可能有问题（如循环依赖）
        # 这里我们直接结束，但在复杂应用中可以添加错误处理分支
        print("--- 调度器未找到可执行任务，流程结束 ---")
        return "end_process"


# --- 6. 构建并编译图 (Build and Compile the Graph) ---

# 创建图工作流
workflow = StateGraph(GraphState)

# 添加节点
workflow.add_node("planner", planner_node)
workflow.add_node("dispatcher", dispatcher_node)
workflow.add_node("tool_executor", tool_executor_node)
workflow.add_node("final_response_generator", final_response_node)

# 设置入口点
workflow.set_entry_point("planner")

# 添加边
workflow.add_edge("planner", "dispatcher")

workflow.add_conditional_edges(
    "dispatcher",
    dispatcher_check,
    {
        "execute_tools": "tool_executor",
        "end_process": "final_response_generator"
    }
)

workflow.add_conditional_edges(
    "tool_executor",
    should_continue,
    {
        "continue_execution": "dispatcher",  # 循环回到调度器
        "end_process": "final_response_generator"
    }
)

workflow.add_edge("final_response_generator", END)

# 编译图
app = workflow.compile()

# --- 7. 执行图并查看结果 ---

if __name__ == "__main__":
    # 定义一个需要多个步骤和依赖关系的请求
    request = "请帮我查找有关多模态大模型的最新研究，并基于这些研究的核心思想，用Python编写一个概念性的代码片段。"

    # 初始化状态
    initial_state = {
        "initial_request": request,
        "plan": [],
        "executed_tasks": [],
        "tool_results": [],
        "tasks_to_execute": [],
        "final_response": "",
    }

    print("--- 开始执行 LangGraph 流程 ---")
    # 使用 stream() 方法可以清晰地看到每一步的状态变化
    for s in app.stream(initial_state, {"recursion_limit": 10}):
        print("=" * 60)
        state_key = list(s.keys())[0]
        print(f"当前节点: {state_key}")
        # print(f"当前状态: {s[state_key]}") # 打印详细状态以供调试

    # 使用 invoke() 可以直接获取最终结果
    final_result = app.invoke(initial_state, {"recursion_limit": 10})

    print("\n\n" + "=" * 30 + " 最终结果 " + "=" * 30)
    print(f"原始请求: {final_result['initial_request']}")
    print("\n--- 执行计划 (DAG) ---")
    print(json.dumps(final_result['plan'], indent=2, ensure_ascii=False))
    print("\n--- 最终答复 ---")
    print(final_result.get("final_response"))
    print("\n" + "=" * 70)