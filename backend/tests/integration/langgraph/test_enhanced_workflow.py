#!/usr/bin/env python3
"""
增强版LangGraph工作流测试 - 更好地展示动态DAG和并行执行能力

核心需求覆盖：
1. 动态DAG：Planner根据不同问题生成不同的执行计划
2. 并行执行：真正的并行执行无依赖任务，降低响应时延
3. 完整工具文档：每个工具都有完整的文档信息
"""

import os
import json
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import TypedDict, Annotated, List, Dict, Any
from dataclasses import dataclass
from langchain_core.messages import BaseMessage, HumanMessage
from langgraph.graph import StateGraph, END
import operator

from deep_diagnose.llms.llm import get_llm_by_type


# --- 1. 工具文档和注册系统 ---

@dataclass
class ToolDocumentation:
    """完整的工具文档，满足一般需求1：工具"""
    name: str
    brief_description: str  # 用于Planner决策的简短说明
    full_description: str   # 完整工具简介
    input_params: Dict[str, str]  # 入参说明
    output_description: str  # 出参说明
    usage_examples: List[str]  # 调用示例
    execution_time_estimate: float  # 预估执行时间（秒）


# 定义工具文档
TOOL_DOCS = {
    "search": ToolDocumentation(
        name="search",
        brief_description="网络搜索工具，用于获取最新信息",
        full_description="通过搜索引擎API获取相关主题的最新信息和研究资料",
        input_params={"query": "搜索查询字符串"},
        output_description="返回搜索结果摘要，包含相关信息和链接",
        usage_examples=["search('多模态大模型最新研究')", "search('ECS实例诊断方法')"],
        execution_time_estimate=2.0
    ),
    "analyze_data": ToolDocumentation(
        name="analyze_data",
        brief_description="数据分析工具，用于处理和分析结构化数据",
        full_description="对输入的数据进行统计分析、模式识别和趋势分析",
        input_params={"data": "要分析的数据", "analysis_type": "分析类型"},
        output_description="返回分析结果，包含统计信息、图表和结论",
        usage_examples=["analyze_data(data, 'trend')", "analyze_data(metrics, 'correlation')"],
        execution_time_estimate=3.0
    ),
    "write_code": ToolDocumentation(
        name="write_code",
        brief_description="代码生成工具，根据需求生成代码片段",
        full_description="基于用户需求和技术规范生成高质量的代码实现",
        input_params={"requirements": "代码需求描述", "language": "编程语言"},
        output_description="返回完整的代码实现，包含注释和使用说明",
        usage_examples=["write_code('多模态模型类', 'python')", "write_code('API接口', 'java')"],
        execution_time_estimate=4.0
    ),
    "diagnose_system": ToolDocumentation(
        name="diagnose_system",
        brief_description="系统诊断工具，用于分析系统问题",
        full_description="对系统状态进行全面诊断，识别性能瓶颈和潜在问题",
        input_params={"system_info": "系统信息", "symptoms": "问题症状"},
        output_description="返回诊断报告，包含问题分析和解决建议",
        usage_examples=["diagnose_system(info, 'high_cpu')", "diagnose_system(logs, 'memory_leak')"],
        execution_time_estimate=5.0
    ),
    "generate_report": ToolDocumentation(
        name="generate_report",
        brief_description="报告生成工具，整合信息生成结构化报告",
        full_description="将多个数据源的信息整合成专业的分析报告",
        input_params={"data_sources": "数据源列表", "template": "报告模板"},
        output_description="返回格式化的报告文档，包含图表和总结",
        usage_examples=["generate_report(sources, 'technical')", "generate_report(results, 'executive')"],
        execution_time_estimate=3.5
    )
}


# --- 2. 增强的状态定义 ---

class EnhancedGraphState(TypedDict):
    """增强的图状态，支持动态DAG和并行执行监控"""
    initial_request: str
    request_type: str  # 请求类型，用于动态DAG生成
    plan: List[Dict]
    executed_tasks: Annotated[List[int], operator.add]
    tool_results: Annotated[List[Dict], operator.add]
    tasks_to_execute: List[Dict]
    parallel_execution_log: Annotated[List[Dict], operator.add]  # 并行执行日志
    final_response: str
    execution_metrics: Dict[str, Any]  # 执行指标


# --- 3. 真正的并行工具执行函数 ---

def search_tool(query: str) -> Dict[str, Any]:
    """网络搜索工具 - 模拟真实的搜索延迟"""
    start_time = time.time()
    print(f"[{time.strftime('%H:%M:%S')}] 🔍 开始搜索: {query}")
    
    # 模拟网络搜索延迟
    time.sleep(TOOL_DOCS["search"].execution_time_estimate)
    
    result = {
        "content": f"关于'{query}'的搜索结果：发现了最新的研究进展和技术趋势。",
        "sources": ["paper1.pdf", "article2.html", "blog3.md"],
        "execution_time": time.time() - start_time
    }
    
    print(f"[{time.strftime('%H:%M:%S')}] ✅ 搜索完成: {query}")
    return result


def analyze_data_tool(data: str) -> Dict[str, Any]:
    """数据分析工具 - 模拟数据处理延迟"""
    start_time = time.time()
    print(f"[{time.strftime('%H:%M:%S')}] 📊 开始数据分析: {data}")
    
    # 模拟数据分析延迟
    time.sleep(TOOL_DOCS["analyze_data"].execution_time_estimate)
    
    result = {
        "content": f"对'{data}'的分析结果：识别出关键模式和趋势。",
        "metrics": {"accuracy": 0.95, "confidence": 0.88},
        "execution_time": time.time() - start_time
    }
    
    print(f"[{time.strftime('%H:%M:%S')}] ✅ 数据分析完成: {data}")
    return result


def write_code_tool(requirements: str) -> Dict[str, Any]:
    """代码生成工具 - 模拟代码生成延迟"""
    start_time = time.time()
    print(f"[{time.strftime('%H:%M:%S')}] 💻 开始代码生成: {requirements}")
    
    # 模拟代码生成延迟
    time.sleep(TOOL_DOCS["write_code"].execution_time_estimate)
    
    result = {
        "content": f"""
# 基于需求: {requirements}
class EnhancedMultiModalModel:
    def __init__(self):
        self.initialized = True
        
    def process(self, inputs):
        return f"处理结果: {{inputs}}"
""",
        "language": "python",
        "execution_time": time.time() - start_time
    }
    
    print(f"[{time.strftime('%H:%M:%S')}] ✅ 代码生成完成: {requirements}")
    return result


def diagnose_system_tool(system_info: str) -> Dict[str, Any]:
    """系统诊断工具 - 模拟诊断延迟"""
    start_time = time.time()
    print(f"[{time.strftime('%H:%M:%S')}] 🔧 开始系统诊断: {system_info}")
    
    # 模拟系统诊断延迟
    time.sleep(TOOL_DOCS["diagnose_system"].execution_time_estimate)
    
    result = {
        "content": f"系统'{system_info}'诊断完成：发现性能瓶颈和优化建议。",
        "issues_found": ["高CPU使用率", "内存泄漏风险"],
        "recommendations": ["优化算法", "增加内存"],
        "execution_time": time.time() - start_time
    }
    
    print(f"[{time.strftime('%H:%M:%S')}] ✅ 系统诊断完成: {system_info}")
    return result


def generate_report_tool(data_sources: str) -> Dict[str, Any]:
    """报告生成工具 - 模拟报告生成延迟"""
    start_time = time.time()
    print(f"[{time.strftime('%H:%M:%S')}] 📝 开始报告生成: {data_sources}")
    
    # 模拟报告生成延迟
    time.sleep(TOOL_DOCS["generate_report"].execution_time_estimate)
    
    result = {
        "content": f"基于'{data_sources}'生成的综合报告已完成。",
        "sections": ["摘要", "详细分析", "结论和建议"],
        "execution_time": time.time() - start_time
    }
    
    print(f"[{time.strftime('%H:%M:%S')}] ✅ 报告生成完成: {data_sources}")
    return result


# 工具注册表
ENHANCED_TOOL_REGISTRY = {
    "search": search_tool,
    "analyze_data": analyze_data_tool,
    "write_code": write_code_tool,
    "diagnose_system": diagnose_system_tool,
    "generate_report": generate_report_tool,
}


# --- 4. 动态DAG规划节点 ---

def dynamic_planner_node(state: EnhancedGraphState):
    """
    动态规划节点：根据请求类型生成不同的DAG执行计划
    核心需求1：动态DAG
    """
    print("\n🎯 >>> 进入动态规划节点 (Dynamic Planner Node)")
    
    # 分析请求类型
    request = state['initial_request']
    request_type = classify_request_type(request)
    
    print(f"📋 请求类型识别: {request_type}")
    
    # 根据请求类型生成不同的工具列表和提示
    if request_type == "research_and_development":
        available_tools = ["search", "analyze_data", "write_code", "generate_report"]
        planning_context = "这是一个研究开发类请求，需要搜索信息、分析数据、生成代码和整合报告。"
    elif request_type == "system_diagnosis":
        available_tools = ["diagnose_system", "analyze_data", "search", "generate_report"]
        planning_context = "这是一个系统诊断类请求，需要诊断系统、分析数据、搜索解决方案和生成报告。"
    elif request_type == "data_analysis":
        available_tools = ["search", "analyze_data", "generate_report"]
        planning_context = "这是一个数据分析类请求，需要搜索相关信息、分析数据和生成报告。"
    else:
        available_tools = list(ENHANCED_TOOL_REGISTRY.keys())
        planning_context = "这是一个综合类请求，可以使用所有可用工具。"
    
    # 构建工具文档字符串
    tools_doc = "\\n".join([
        f"- {tool}: {TOOL_DOCS[tool].brief_description} (预估耗时: {TOOL_DOCS[tool].execution_time_estimate}秒)"
        for tool in available_tools
    ])
    
    prompt = f"""
你是一个专业的动态任务规划师。{planning_context}

可用工具：
{tools_doc}

请根据用户请求生成一个优化的DAG执行计划，重点考虑：
1. 任务之间的逻辑依赖关系
2. 并行执行的可能性（无依赖的任务应该并行执行）
3. 执行效率和响应时延

输出JSON格式的任务列表，每个任务包含：
- "id": 任务唯一标识（整数，从1开始）
- "task": 任务具体描述
- "tool": 工具名称（必须在可用工具列表中）
- "dependencies": 依赖的任务ID列表
- "estimated_time": 预估执行时间（秒）
- "priority": 优先级（1-5，5最高）

用户请求: "{request}"
"""
    
    llm = get_llm_by_type("reasoning")
    response = llm.invoke(prompt)
    
    # 解析JSON响应
    content = response.content.strip()
    print(f"🔍 LLM原始响应: {content[:200]}...")  # 调试信息
    
    # 更智能的JSON提取
    if "```json" in content:
        start_idx = content.find("```json") + 7
        end_idx = content.find("```", start_idx)
        if end_idx != -1:
            content = content[start_idx:end_idx].strip()
    elif "```" in content:
        start_idx = content.find("```") + 3
        end_idx = content.find("```", start_idx)
        if end_idx != -1:
            content = content[start_idx:end_idx].strip()
    else:
        # 尝试找到JSON数组或对象的开始
        json_start = max(content.find('['), content.find('{'))
        if json_start != -1:
            content = content[json_start:]
    
    try:
        plan_data = json.loads(content)
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        print(f"🔍 尝试解析的内容: {content}")
        # 提供一个默认的计划作为fallback
        plan_data = [
            {"id": 1, "task": "搜索相关信息", "tool": "search", "dependencies": [], "estimated_time": 2.0, "priority": 3},
            {"id": 2, "task": "分析数据", "tool": "analyze_data", "dependencies": [1], "estimated_time": 3.0, "priority": 3},
            {"id": 3, "task": "生成代码", "tool": "write_code", "dependencies": [2], "estimated_time": 4.0, "priority": 3}
        ]
    
    # 处理不同的JSON格式
    if isinstance(plan_data, dict) and "tasks" in plan_data:
        plan = plan_data["tasks"]
    elif isinstance(plan_data, list):
        plan = plan_data
    else:
        plan = [plan_data] if isinstance(plan_data, dict) else []
    
    print("📊 --- 动态生成的执行计划 (DAG) ---")
    print(json.dumps(plan, indent=2, ensure_ascii=False))
    
    # 分析并行执行潜力
    parallel_groups = analyze_parallel_potential(plan)
    print(f"🔄 并行执行分析: 发现 {len(parallel_groups)} 个并行执行组")
    
    return {
        "plan": plan,
        "request_type": request_type,
        "execution_metrics": {
            "total_tasks": len(plan),
            "parallel_groups": len(parallel_groups),
            "estimated_total_time": calculate_estimated_time(plan)
        }
    }


def classify_request_type(request: str) -> str:
    """分类请求类型以支持动态DAG生成"""
    request_lower = request.lower()
    
    if any(keyword in request_lower for keyword in ["研究", "开发", "代码", "实现", "设计"]):
        return "research_and_development"
    elif any(keyword in request_lower for keyword in ["诊断", "问题", "故障", "性能", "系统"]):
        return "system_diagnosis"
    elif any(keyword in request_lower for keyword in ["分析", "数据", "统计", "趋势"]):
        return "data_analysis"
    else:
        return "general"


def analyze_parallel_potential(plan: List[Dict]) -> List[List[int]]:
    """分析DAG中的并行执行潜力"""
    parallel_groups = []
    processed_tasks = set()
    
    while len(processed_tasks) < len(plan):
        current_group = []
        for task in plan:
            task_id = task["id"]
            if task_id in processed_tasks:
                continue
            
            # 检查依赖是否都已处理
            dependencies = task.get("dependencies", [])
            if all(dep in processed_tasks for dep in dependencies):
                current_group.append(task_id)
        
        if current_group:
            parallel_groups.append(current_group)
            processed_tasks.update(current_group)
        else:
            break  # 避免无限循环
    
    return parallel_groups


def calculate_estimated_time(plan: List[Dict]) -> float:
    """计算考虑并行执行的预估总时间"""
    parallel_groups = analyze_parallel_potential(plan)
    total_time = 0
    
    for group in parallel_groups:
        # 每个并行组的时间是组内最长任务的时间
        group_max_time = 0
        for task_id in group:
            task = next(t for t in plan if t["id"] == task_id)
            task_time = task.get("estimated_time", 3.0)
            group_max_time = max(group_max_time, task_time)
        total_time += group_max_time
    
    return total_time


# --- 5. 真正的并行执行节点 ---

def parallel_tool_executor_node(state: EnhancedGraphState):
    """
    并行工具执行节点：真正实现并行执行
    核心需求2：并行执行
    """
    print("\n⚡ >>> 进入并行工具执行节点 (Parallel Tool Executor Node)")
    
    tasks_to_execute = state["tasks_to_execute"]
    if not tasks_to_execute:
        print("--- 没有需要执行的新任务 ---")
        return {}
    
    print(f"🔄 准备并行执行 {len(tasks_to_execute)} 个任务")
    
    # 记录并行执行开始时间
    parallel_start_time = time.time()
    
    # 使用ThreadPoolExecutor实现真正的并行执行
    results = {}
    execution_log = []
    
    with ThreadPoolExecutor(max_workers=len(tasks_to_execute)) as executor:
        # 提交所有任务
        future_to_task = {}
        for task in tasks_to_execute:
            if task["tool"] in ENHANCED_TOOL_REGISTRY:
                tool_func = ENHANCED_TOOL_REGISTRY[task["tool"]]
                future = executor.submit(tool_func, task["task"])
                future_to_task[future] = task
        
        # 收集结果
        for future in as_completed(future_to_task):
            task = future_to_task[future]
            try:
                result = future.result()
                results[str(task["id"])] = result
                
                execution_log.append({
                    "task_id": task["id"],
                    "tool": task["tool"],
                    "start_time": parallel_start_time,
                    "execution_time": result.get("execution_time", 0),
                    "status": "completed"
                })
                
            except Exception as exc:
                print(f"❌ 任务 {task['id']} 执行失败: {exc}")
                execution_log.append({
                    "task_id": task["id"],
                    "tool": task["tool"],
                    "start_time": parallel_start_time,
                    "execution_time": 0,
                    "status": "failed",
                    "error": str(exc)
                })
    
    parallel_end_time = time.time()
    total_parallel_time = parallel_end_time - parallel_start_time
    
    print(f"⚡ 并行执行完成，总耗时: {total_parallel_time:.2f}秒")
    
    # 计算如果串行执行需要的时间
    serial_time = sum(log.get("execution_time", 0) for log in execution_log)
    time_saved = serial_time - total_parallel_time
    
    print(f"📈 性能提升: 串行需要{serial_time:.2f}秒，并行节省{time_saved:.2f}秒 ({time_saved/serial_time*100:.1f}%)")
    
    # 格式化结果
    newly_executed_ids = [int(task_id) for task_id in results.keys()]
    formatted_results = [
        {
            "task_id": int(tid), 
            "result": res.get("content", str(res)),
            "execution_time": res.get("execution_time", 0)
        } 
        for tid, res in results.items()
    ]
    
    print("📋 --- 并行执行结果 ---")
    for result in formatted_results:
        print(f"任务{result['task_id']}: 耗时{result['execution_time']:.2f}秒")
    
    return {
        "executed_tasks": newly_executed_ids,
        "tool_results": formatted_results,
        "parallel_execution_log": execution_log
    }


# --- 6. 其他节点保持不变但增强 ---

def enhanced_dispatcher_node(state: EnhancedGraphState):
    """增强的调度节点"""
    print("\n🎛️ >>> 进入增强调度节点 (Enhanced Dispatcher Node)")
    
    plan = state["plan"]
    executed_tasks = state["executed_tasks"]
    
    # 找出所有依赖项都已满足的任务
    ready_tasks = []
    for task in plan:
        if task["id"] in executed_tasks:
            continue
        
        all_dependencies_met = all(dep in executed_tasks for dep in task.get("dependencies", []))
        if all_dependencies_met:
            ready_tasks.append(task)
    
    # 按优先级排序
    ready_tasks.sort(key=lambda x: x.get("priority", 3), reverse=True)
    
    task_list = [f'ID{task["id"]}({task["tool"]})' for task in ready_tasks]
    print(f"📋 准备并行执行的任务: {task_list}")
    
    return {"tasks_to_execute": ready_tasks}


def enhanced_final_response_node(state: EnhancedGraphState):
    """增强的最终响应节点"""
    print("\n📝 >>> 进入增强最终响应节点 (Enhanced Final Response Node)")
    
    # 计算总体执行指标
    total_execution_time = sum(
        log.get("execution_time", 0) 
        for log in state.get("parallel_execution_log", [])
    )
    
    parallel_groups = len(set(
        log.get("start_time", 0) 
        for log in state.get("parallel_execution_log", [])
    ))
    
    prompt = f"""
你是一个专业的总结专家。根据用户的原始请求和工具执行结果，生成一个全面的最终答复。

原始请求: {state['initial_request']}
请求类型: {state.get('request_type', 'unknown')}

执行统计:
- 总任务数: {len(state['plan'])}
- 并行执行组数: {parallel_groups}
- 总执行时间: {total_execution_time:.2f}秒

工具执行结果:
{json.dumps(state['tool_results'], indent=2, ensure_ascii=False)}

请生成一个结构化的最终答复，包含：
1. 执行摘要
2. 主要发现和结果
3. 性能统计
4. 结论和建议
"""
    
    llm = get_llm_by_type("basic")
    response = llm.invoke(prompt)
    
    return {"final_response": response.content}


# --- 7. 条件判断函数 ---

def enhanced_should_continue(state: EnhancedGraphState):
    """增强的继续判断"""
    print("\n🔍 >>> 进入增强条件判断 (Enhanced Should Continue?)")
    
    if len(state["executed_tasks"]) == len(state["plan"]):
        print("✅ 所有任务完成，准备生成最终答复")
        return "end_process"
    else:
        remaining = len(state["plan"]) - len(state["executed_tasks"])
        print(f"🔄 还有 {remaining} 个任务未完成，继续调度")
        return "continue_execution"


def enhanced_dispatcher_check(state: EnhancedGraphState):
    """增强的调度检查"""
    if state["tasks_to_execute"]:
        return "execute_tools"
    else:
        print("⚠️ 调度器未找到可执行任务，可能存在循环依赖")
        return "end_process"


# --- 8. 构建增强的工作流 ---

def create_enhanced_workflow():
    """创建增强的工作流"""
    workflow = StateGraph(EnhancedGraphState)
    
    # 添加节点
    workflow.add_node("dynamic_planner", dynamic_planner_node)
    workflow.add_node("enhanced_dispatcher", enhanced_dispatcher_node)
    workflow.add_node("parallel_executor", parallel_tool_executor_node)
    workflow.add_node("enhanced_final_response", enhanced_final_response_node)
    
    # 设置入口点
    workflow.set_entry_point("dynamic_planner")
    
    # 添加边
    workflow.add_edge("dynamic_planner", "enhanced_dispatcher")
    
    workflow.add_conditional_edges(
        "enhanced_dispatcher",
        enhanced_dispatcher_check,
        {
            "execute_tools": "parallel_executor",
            "end_process": "enhanced_final_response"
        }
    )
    
    workflow.add_conditional_edges(
        "parallel_executor",
        enhanced_should_continue,
        {
            "continue_execution": "enhanced_dispatcher",
            "end_process": "enhanced_final_response"
        }
    )
    
    workflow.add_edge("enhanced_final_response", END)
    
    return workflow.compile()


# --- 9. 测试用例 ---

def test_research_and_development():
    """测试研究开发类请求"""
    print("🧪 === 测试用例1: 研究开发类请求 ===")
    
    app = create_enhanced_workflow()
    
    request = "请帮我研究多模态大模型的最新进展，分析其核心技术，并设计一个Python实现框架，最后生成技术报告。"
    
    initial_state = {
        "initial_request": request,
        "request_type": "",
        "plan": [],
        "executed_tasks": [],
        "tool_results": [],
        "tasks_to_execute": [],
        "parallel_execution_log": [],
        "final_response": "",
        "execution_metrics": {}
    }
    
    print(f"📝 请求: {request}")
    print("🚀 开始执行增强工作流...")
    
    start_time = time.time()
    final_result = app.invoke(initial_state, {"recursion_limit": 15})
    end_time = time.time()
    
    print(f"\n⏱️ 总执行时间: {end_time - start_time:.2f}秒")
    print(f"📊 执行指标: {final_result.get('execution_metrics', {})}")
    print(f"🔄 并行执行日志条数: {len(final_result.get('parallel_execution_log', []))}")
    
    return final_result


def test_system_diagnosis():
    """测试系统诊断类请求"""
    print("\n🧪 === 测试用例2: 系统诊断类请求 ===")
    
    app = create_enhanced_workflow()
    
    request = "我的ECS实例出现高CPU使用率问题，请帮我诊断系统状态，分析性能数据，搜索解决方案，并生成诊断报告。"
    
    initial_state = {
        "initial_request": request,
        "request_type": "",
        "plan": [],
        "executed_tasks": [],
        "tool_results": [],
        "tasks_to_execute": [],
        "parallel_execution_log": [],
        "final_response": "",
        "execution_metrics": {}
    }
    
    print(f"📝 请求: {request}")
    print("🚀 开始执行增强工作流...")
    
    start_time = time.time()
    final_result = app.invoke(initial_state, {"recursion_limit": 15})
    end_time = time.time()
    
    print(f"\n⏱️ 总执行时间: {end_time - start_time:.2f}秒")
    print(f"📊 执行指标: {final_result.get('execution_metrics', {})}")
    
    return final_result


def test_parallel_execution():
    """测试真正的并行执行能力"""
    print("\n🧪 === 测试用例3: 真正的并行执行 ===")
    
    app = create_enhanced_workflow()
    
    # 设计一个能产生真正并行执行的请求
    request = "请同时进行以下独立任务：搜索AI最新趋势，分析当前市场数据，编写示例代码，然后基于所有结果生成综合报告。"
    
    initial_state = {
        "initial_request": request,
        "request_type": "",
        "plan": [],
        "executed_tasks": [],
        "tool_results": [],
        "tasks_to_execute": [],
        "parallel_execution_log": [],
        "final_response": "",
        "execution_metrics": {}
    }
    
    print(f"📝 请求: {request}")
    print("🚀 开始执行增强工作流...")
    
    start_time = time.time()
    final_result = app.invoke(initial_state, {"recursion_limit": 15})
    end_time = time.time()
    
    print(f"\n⏱️ 总执行时间: {end_time - start_time:.2f}秒")
    print(f"📊 执行指标: {final_result.get('execution_metrics', {})}")
    
    # 分析并行执行效果
    parallel_logs = final_result.get('parallel_execution_log', [])
    if parallel_logs:
        # 找出真正并行执行的任务组
        start_times = {}
        for log in parallel_logs:
            start_time_key = log.get('start_time', 0)
            if start_time_key not in start_times:
                start_times[start_time_key] = []
            start_times[start_time_key].append(log)
        
        print(f"\n🔄 并行执行分析:")
        for start_time_key, tasks in start_times.items():
            if len(tasks) > 1:
                task_ids = [task['task_id'] for task in tasks]
                total_time = sum(task.get('execution_time', 0) for task in tasks)
                actual_time = max(task.get('execution_time', 0) for task in tasks)
                time_saved = total_time - actual_time
                print(f"  📈 并行组: 任务{task_ids} 同时执行")
                print(f"     串行需要: {total_time:.2f}秒, 并行实际: {actual_time:.2f}秒")
                print(f"     节省时间: {time_saved:.2f}秒 ({time_saved/total_time*100:.1f}%)")
    
    return final_result


if __name__ == "__main__":
    print("🎯 增强版LangGraph工作流测试")
    print("=" * 60)
    
    # 测试不同类型的请求，展示动态DAG能力
    result1 = test_research_and_development()
    result2 = test_system_diagnosis()
    result3 = test_parallel_execution()
    
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print("✅ 动态DAG: 根据不同请求类型生成不同的执行计划")
    print("✅ 并行执行: 真正实现了无依赖任务的并行执行")
    print("✅ 工具文档: 每个工具都有完整的文档信息")
    print("✅ 性能监控: 详细的执行时间和并行效果统计")
    print("✅ 核心需求覆盖:")
    print("   - 核心需求1: 动态DAG生成 ✓")
    print("   - 核心需求2: 真正的并行执行 ✓") 
    print("   - 一般需求1: 完整工具文档 ✓")