#!/usr/bin/env python3
"""
测试多个工具并发执行的集成测试

验证 create_react_agent 是否能够同时调用多个 MCP 工具，
模拟实际的 ECS 实例诊断场景。
"""

import asyncio
import pytest
import logging
from typing import List, Dict, Any
from unittest.mock import AsyncMock, MagicMock, patch

from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.runnables import RunnableConfig
from langgraph.prebuilt import create_react_agent

# 导入项目模块
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../src'))

from deep_diagnose.core.reasoning.agents.utils import setup_mcp_tools, get_recursion_limit
from deep_diagnose.llms.llm import get_llm_by_type
from deep_diagnose.common.config.constants.agents import AGENT_LLM_MAP
from deep_diagnose.common.config.core.configuration import Configuration

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MockMCPTool:
    """模拟 MCP 工具"""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
    
    async def ainvoke(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """模拟工具执行"""
        await asyncio.sleep(0.1)  # 模拟网络延迟
        
        # 根据工具名称返回不同的模拟数据
        if self.name == "getVmBasicInfo":
            return {
                "instanceId": input_data.get("instanceId"),
                "status": "Stopped",
                "createTime": "2025-06-25T10:00:00Z",
                "region": "cn-hangzhou"
            }
        elif self.name == "listVmHostHistory":
            return {
                "instanceId": input_data.get("instanceId"),
                "hostHistory": [
                    {
                        "hostId": "host-123",
                        "startTime": "2025-06-26T01:00:00Z",
                        "endTime": "2025-06-26T04:00:00Z",
                        "event": "migration"
                    }
                ]
            }
        elif self.name == "listReportedOperationalEvents":
            return {
                "instanceId": input_data.get("instanceId"),
                "events": [
                    {
                        "eventType": "SystemMaintenance",
                        "startTime": "2025-06-26T01:30:00Z",
                        "endTime": "2025-06-26T03:30:00Z"
                    }
                ]
            }
        else:
            return {"result": f"Mock result for {self.name}"}


class TestParallelToolsExecution:
    """测试并发工具执行"""
    
    @pytest.fixture
    def mock_config(self):
        """创建模拟配置"""
        config = RunnableConfig(
            configurable={
                "mcp_settings": {
                    "servers": {
                        "diagnose": {
                            "enabled_tools": [
                                "getVmBasicInfo",
                                "listVmHostHistory", 
                                "listReportedOperationalEvents"
                            ],
                            "add_to_agents": ["researcher"]
                        }
                    }
                }
            }
        )
        return config
    
    @pytest.fixture
    def mock_tools(self):
        """创建模拟工具列表"""
        return [
            MockMCPTool("getVmBasicInfo", "获取虚拟机基础信息"),
            MockMCPTool("listVmHostHistory", "查询虚拟机宿主机历史"),
            MockMCPTool("listReportedOperationalEvents", "查询运维事件")
        ]
    
    @pytest.fixture
    def test_instances(self):
        """测试用的实例ID列表"""
        return [
            "i-t4n4vky24zw2w1qnqoyf",
            "i-t4n74bsfzx58x0lj4qbh", 
            "i-t4na3cc0c9mimcw9667x",
            "i-j6ch2zf4qfy1rltbql6r",
            "i-2vc5alcmxz75rw8aol4g"
        ]
    
    @pytest.mark.asyncio
    async def test_parallel_tools_with_create_react_agent(self, mock_config, mock_tools, test_instances):
        """测试使用 create_react_agent 进行并发工具调用"""
        
        # 模拟 LLM 返回多个工具调用
        mock_llm = AsyncMock()
        
        # 构造包含多个工具调用的响应
        mock_response = {
            "messages": [
                AIMessage(
                    content="我将并发调用多个工具来诊断这些实例的问题。",
                    tool_calls=[
                        {
                            "id": "call_1",
                            "name": "getVmBasicInfo",
                            "args": {"instanceId": test_instances[0]}
                        },
                        {
                            "id": "call_2", 
                            "name": "listVmHostHistory",
                            "args": {
                                "instanceId": test_instances[0],
                                "startTime": "2025-06-26 01:00:00",
                                "endTime": "2025-06-26 04:00:00"
                            }
                        },
                        {
                            "id": "call_3",
                            "name": "listReportedOperationalEvents", 
                            "args": {
                                "instanceId": test_instances[0],
                                "startTime": "2025-06-26 01:00:00",
                                "endTime": "2025-06-26 04:00:00"
                            }
                        }
                    ]
                )
            ]
        }
        
        mock_llm.ainvoke.return_value = mock_response
        
        # 确保 LLM 支持并发工具调用
        mock_llm.bind = MagicMock(return_value=mock_llm)
        mock_llm.model_kwargs = {"parallel_tool_calls": True}
        
        # 创建 react agent
        agent = create_react_agent(
            name="researcher",
            model=mock_llm,
            tools=mock_tools,
            prompt=lambda state: "请并发调用多个工具来诊断ECS实例问题"
        )
        
        # 准备输入
        agent_input = {
            "messages": [
                HumanMessage(
                    content=f"""这些实例在2025年6月26日凌晨1-4点发生了不可用，请排查问题的原因。
                    实例列表: {', '.join(test_instances)}
                    
                    请同时调用以下工具进行诊断：
                    1. getVmBasicInfo - 获取实例基础信息
                    2. listVmHostHistory - 查询宿主机迁移历史
                    3. listReportedOperationalEvents - 查询运维事件
                    """
                )
            ]
        }
        
        # 执行 agent
        start_time = asyncio.get_event_loop().time()
        result = await agent.ainvoke(
            input=agent_input,
            config={"recursion_limit": get_recursion_limit()}
        )
        end_time = asyncio.get_event_loop().time()
        
        # 验证结果
        assert result is not None
        assert "messages" in result
        
        # 验证 LLM 被调用
        mock_llm.ainvoke.assert_called()
        
        # 验证响应包含多个工具调用
        response_message = result["messages"][-1]
        if hasattr(response_message, 'tool_calls') and response_message.tool_calls:
            tool_calls = response_message.tool_calls
            logger.info(f"检测到 {len(tool_calls)} 个工具调用")
            
            # 验证包含期望的工具
            tool_names = [call.get("name") for call in tool_calls]
            assert "getVmBasicInfo" in tool_names
            assert "listVmHostHistory" in tool_names
            assert "listReportedOperationalEvents" in tool_names
            
            logger.info(f"并发工具调用成功: {tool_names}")
        else:
            logger.warning("未检测到工具调用，可能是模拟配置问题")
        
        execution_time = end_time - start_time
        logger.info(f"执行时间: {execution_time:.2f}秒")
        
        # 如果是真正的并发执行，时间应该接近单个工具的执行时间
        # 而不是所有工具执行时间的总和
        assert execution_time < 5.0  # 合理的执行时间上限
    
    @pytest.mark.asyncio
    async def test_mcp_tools_setup_for_parallel_execution(self, mock_config):
        """测试 MCP 工具设置是否支持并发执行"""
        
        with patch('deep_diagnose.core.reasoning.agents.utils.MultiServerMCPClient') as mock_client_class:
            # 模拟 MCP 客户端
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            
            # 模拟返回多个工具
            mock_tools = [
                MockMCPTool("getVmBasicInfo", "获取虚拟机基础信息"),
                MockMCPTool("listVmHostHistory", "查询虚拟机宿主机历史"),
                MockMCPTool("listReportedOperationalEvents", "查询运维事件")
            ]
            mock_client.get_tools.return_value = mock_tools
            
            # 调用 setup_mcp_tools
            tools = await setup_mcp_tools("researcher", mock_config)
            
            # 验证返回了多个工具
            assert len(tools) >= 3
            tool_names = [tool.name for tool in tools]
            assert "getVmBasicInfo" in tool_names
            assert "listVmHostHistory" in tool_names
            assert "listReportedOperationalEvents" in tool_names
            
            logger.info(f"成功设置了 {len(tools)} 个 MCP 工具")
    
    @pytest.mark.asyncio
    async def test_llm_parallel_tool_calls_configuration(self):
        """测试 LLM 的并发工具调用配置"""
        
        # 模拟 LLM 配置
        with patch('deep_diagnose.llms.llm.get_llm_by_type') as mock_get_llm:
            mock_llm = AsyncMock()
            mock_llm.bind = MagicMock(return_value=mock_llm)
            mock_llm.model_kwargs = {}
            mock_get_llm.return_value = mock_llm
            
            # 获取 LLM 并配置并发工具调用
            llm = get_llm_by_type("reasoning")
            
            # 测试 bind 方法配置并发工具调用
            if hasattr(llm, 'bind'):
                bound_llm = llm.bind(parallel_tool_calls=True)
                logger.info("成功配置 LLM 支持并发工具调用")
                
                # 验证配置生效
                assert bound_llm is not None
            else:
                pytest.fail("LLM 不支持 bind 方法")
    
    @pytest.mark.asyncio
    async def test_end_to_end_parallel_execution_simulation(self, mock_config, test_instances):
        """端到端并发执行模拟测试"""
        
        logger.info("开始端到端并发执行测试")
        
        # 模拟完整的执行流程
        with patch('deep_diagnose.core.reasoning.agents.utils.setup_mcp_tools') as mock_setup_tools, \
             patch('deep_diagnose.llms.llm.get_llm_by_type') as mock_get_llm:
            
            # 设置模拟工具
            mock_tools = [
                MockMCPTool("getVmBasicInfo", "获取虚拟机基础信息"),
                MockMCPTool("listVmHostHistory", "查询虚拟机宿主机历史"),
                MockMCPTool("listReportedOperationalEvents", "查询运维事件")
            ]
            mock_setup_tools.return_value = mock_tools
            
            # 设置模拟 LLM
            mock_llm = AsyncMock()
            mock_llm.bind = MagicMock(return_value=mock_llm)
            mock_llm.model_kwargs = {"parallel_tool_calls": True}
            
            # 模拟 LLM 返回包含多个工具调用的响应
            mock_response = {
                "messages": [
                    AIMessage(
                        content="正在并发执行多个诊断工具...",
                        tool_calls=[
                            {
                                "id": f"call_{i}",
                                "name": tool.name,
                                "args": {"instanceId": test_instances[0]}
                            }
                            for i, tool in enumerate(mock_tools)
                        ]
                    )
                ]
            }
            mock_llm.ainvoke.return_value = mock_response
            mock_get_llm.return_value = mock_llm
            
            # 创建 agent（模拟 create_agent_with_tools 的逻辑）
            llm = get_llm_by_type("reasoning")
            
            # 确保启用并发工具调用
            if hasattr(llm, 'bind'):
                llm = llm.bind(parallel_tool_calls=True)
            
            tools = await setup_mcp_tools("researcher", mock_config)
            
            agent = create_react_agent(
                name="researcher",
                model=llm,
                tools=tools,
                prompt=lambda state: "请并发调用多个工具进行 ECS 实例诊断"
            )
            
            # 准备输入（模拟 step_executor 的输入）
            agent_input = {
                "messages": [
                    HumanMessage(
                        content=f"""这些实例在2025年6月26日凌晨1-4点发生了不可用，请排查问题的原因。
                        ({', '.join(test_instances)})
                        
                        请同时调用多个工具进行并发诊断以提高效率。
                        """
                    )
                ]
            }
            
            # 执行 agent
            start_time = asyncio.get_event_loop().time()
            result = await agent.ainvoke(
                input=agent_input,
                config={"recursion_limit": get_recursion_limit()}
            )
            end_time = asyncio.get_event_loop().time()
            
            # 验证结果
            assert result is not None
            assert "messages" in result
            
            # 验证执行时间（并发执行应该更快）
            execution_time = end_time - start_time
            logger.info(f"端到端执行时间: {execution_time:.2f}秒")
            
            # 验证工具被正确设置
            mock_setup_tools.assert_called_once_with("researcher", mock_config)
            
            # 验证 LLM 被调用
            mock_llm.ainvoke.assert_called()
            
            logger.info("端到端并发执行测试完成")


if __name__ == "__main__":
    # 运行特定测试
    pytest.main([__file__, "-v", "-s"])