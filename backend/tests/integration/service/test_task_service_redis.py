"""
测试任务服务
"""

import json
import uuid

import pytest

from deep_diagnose.storage.redis_client import RedisClient  # Import real RedisClient
from deep_diagnose.api.models.model import TaskStatus, TaskStatusResponse
from deep_diagnose.services.task.task_service import TaskService


class TestTaskService:
    """测试TaskService类"""
    
    @pytest.fixture(scope="class")
    def task_service(self):
        """创建任务服务实例，使用真实的RedisClient"""
        # This test fixture now uses a real RedisClient.
        # Ensure your Redis server is running and accessible for these tests to pass.
        # For a clean test environment, consider using a dedicated test Redis instance
        # or flushing the database before/after tests.
        return TaskService()
    
    @pytest.fixture(autouse=True)
    async def cleanup_redis(self, task_service):
        """每次测试后清理Redis中的测试数据"""
        # This fixture will attempt to delete all keys with the test prefix after each test.
        # Be cautious if running against a production Redis instance.
        redis_client = RedisClient()
        keys = redis_client.get_cache_keys(f"cloudbot_agent:*")
        if keys:
            redis_client.delete_cache(*keys)
        yield

    def test_get_task_key(self, task_service):
        """测试获取任务键名"""
        # Arrange
        task_id = "test-task-id"
        expected_key = f"cloudbot_agent:{task_id}"

        # Act
        actual_key = f"cloudbot_agent:{task_id}"

        # Assert
        assert actual_key == expected_key
    
    @pytest.mark.asyncio
    async def test_create_task(self, task_service):
        """测试创建任务"""
        # Arrange
        agent = "DiagnoseAgent"
        question = "测试问题"
        user_id = "test_user"

        # Act
        task_id = await task_service.create_task(agent, question, user_id)

        # Assert
        assert task_id is not None
        assert len(task_id) > 0

        # Verify data directly from Redis
        redis_client = RedisClient()
        stored_data_str = redis_client.get_cache(f"cloudbot_agent:{task_id}")
        assert stored_data_str is not None
        task_data = json.loads(stored_data_str)
        assert task_data["task_id"] == task_id
        assert task_data["agent"] == agent
        assert task_data["question"] == question
        assert task_data["user_id"] == user_id
        assert task_data["status"] == TaskStatus.PENDING.value
        assert "submitted_at" in task_data
        assert task_data["completed_at"] is None
    
    @pytest.mark.asyncio
    async def test_get_task_exists(self, task_service):
        """测试获取存在的任务"""
        # Arrange
        task_id = str(uuid.uuid4())
        task_data_to_store = {
            "task_id": task_id,
            "agent": "DiagnoseAgent",
            "question": "测试问题",
            "user_id": "test_user",
            "status": TaskStatus.SUCCESS.value,
            "submitted_at": "2023-10-27T10:00:00Z",
            "completed_at": "2023-10-27T10:05:00Z",
            "data": {
                "result": "测试结果",
                "detail": "执行过程",
                "error": None
            }
        }

        redis_client = RedisClient()
        redis_client.set_cache(f"cloudbot_agent:{task_id}", json.dumps(task_data_to_store), ttl_seconds=60)

        # Act
        result = await task_service.get_task(task_id)

        # Assert
        assert result is not None
        assert isinstance(result, TaskStatusResponse)
        assert result.task_id == task_id
        assert result.status == TaskStatus.SUCCESS
        assert result.submitted_at == "2023-10-27T10:00:00Z"
        assert result.completed_at == "2023-10-27T10:05:00Z"
        assert result.data is not None
        assert result.data.result == "测试结果"
        assert result.data.detail == "执行过程"
        assert result.data.error is None
    
    @pytest.mark.asyncio
    async def test_get_task_not_exists(self, task_service):
        """测试获取不存在的任务"""
        # Arrange
        task_id = str(uuid.uuid4())

        # Act
        result = await task_service.get_task(task_id)

        # Assert
        assert result is None
    
    @pytest.mark.asyncio
    async def test_get_task_invalid_json(self, task_service):
        """测试获取任务时JSON解析失败"""
        # Arrange
        task_id = str(uuid.uuid4())

        redis_client = RedisClient()
        redis_client.set_cache(f"cloudbot_agent:{task_id}", "invalid json", ttl_seconds=60)

        # Act
        result = await task_service.get_task(task_id)

        # Assert
        assert result is None
