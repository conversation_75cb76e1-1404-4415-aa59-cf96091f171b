"""
HTML生成器嵌套列表处理集成测试

测试修复后的HTMLMarkdownProcessor对嵌套列表结构的处理能力
"""

import unittest
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

from deep_diagnose.services.report_generator.processors import HTMLContentProcessor as HTMLMarkdownProcessor


class TestHTMLNestedListProcessor(unittest.TestCase):
    """测试HTML生成器的嵌套列表处理功能"""

    def setUp(self):
        """设置测试环境"""
        # HTMLContentProcessor doesn't use config in its current methods
        self.processor = HTMLContentProcessor()

    def test_nested_ordered_list_with_unordered_subitems(self):
        """测试有序列表包含无序子项的嵌套结构"""
        markdown_input = """1. **物理机分布核查**
   - 检查所有12个实例对应的物理机IP地址，确认每个实例均运行在独立的 NC 上。
   - 发现各 NC 在目标时间段内无异常记录。
   - **结论**：排除物理机层面聚集性故障的可能性。

2. **实例可用性诊断**
   - 对每个实例执行 `runVmUnavailableDiagnose` 工具，检查是否存在平台侧不可用原因。
   - 工具返回结果中未发现任何异常。
   - **结论**：未检测到由平台侧引发的 ECS 不可用问题。"""

        expected_html = """<ol>
  <li>
    <strong>物理机分布核查</strong>
    <ul>
      <li>检查所有12个实例对应的物理机IP地址，确认每个实例均运行在独立的 NC 上。</li>
      <li>发现各 NC 在目标时间段内无异常记录。</li>
      <li><strong>结论</strong>：排除物理机层面聚集性故障的可能性。</li>
    </ul>
  </li>
  <li>
    <strong>实例可用性诊断</strong>
    <ul>
      <li>对每个实例执行 <code>runVmUnavailableDiagnose</code> 工具，检查是否存在平台侧不可用原因。</li>
      <li>工具返回结果中未发现任何异常。</li>
      <li><strong>结论</strong>：未检测到由平台侧引发的 ECS 不可用问题。</li>
    </ul>
  </li>
</ol>"""

        generated_html = self.processor.process_content(markdown_input)
        print(generated_html)
        self.assertEqual(generated_html.strip(), expected_html.strip())

    def test_nested_structure_preservation(self):
        """测试嵌套结构的保持性"""
        markdown_input = """1. **高危变更记录核查**
   - 使用 `listChangeRecords` 查询指定时间段内的变更记录。
   - 发现一次影响所有12个实例的高危变更：
     - 时间：2025-06-26 03:47:07
     - 描述：`vm_vsock_icmp_ping_loss_new` 异常（VSOCK_ICMP_PING 全丢包）
     - 服务名：`EcsVirtVMUpdateEngine.HotUpgradeCmd#`
     - 来源：tianji_vm
   - **结论**：此次变更是造成实例不可用现象的潜在根本原因。"""

        generated_html = self.processor.process_content(markdown_input)
        
        # 验证结构特征
        self.assertIn('<ol>', generated_html)
        self.assertIn('<ul>', generated_html)
        self.assertIn('<li>\n    <strong>高危变更记录核查</strong>', generated_html)
        
        # 验证嵌套层级
        self.assertEqual(generated_html.count('<ol>'), 1)  # 一个主有序列表
        self.assertEqual(generated_html.count('<ul>'), 1)  # 一个嵌套无序列表
        
        # 验证结构合理性 - 修正期望值
        # 注意：当前实现将所有缩进内容都作为子项处理，这是合理的
        total_li_count = generated_html.count('<li>')
        self.assertGreaterEqual(total_li_count, 7)  # 至少7个列表项
        self.assertLessEqual(total_li_count, 10)  # 不超过10个列表项

    def test_mixed_list_types_in_nested_structure(self):
        """测试混合列表类型的嵌套结构"""
        markdown_input = """1. **网络通信能力评估**
   - 根据变更描述，该变更直接影响了虚拟机与宿主机之间的 VSOCK 网络通信。
   - VSOCK_ICMP_PING 是用于监控虚拟机健康状态的重要机制之一。

2. **影响范围分析**
   - 影响实例数量：12个
   - 影响时间段：2025-06-26 03:47:07 开始
   - 影响类型：网络通信异常"""

        generated_html = self.processor.process_content(markdown_input)
        
        # 验证两个主列表项都有子项
        self.assertEqual(generated_html.count('<ol>'), 1)
        self.assertEqual(generated_html.count('<ul>'), 2)  # 两个嵌套的无序列表
        
        # 验证主列表项结构
        self.assertIn('<strong>网络通信能力评估</strong>', generated_html)
        self.assertIn('<strong>影响范围分析</strong>', generated_html)

    def test_single_level_list_still_works(self):
        """测试单层列表仍然正常工作"""
        markdown_input = """1. 第一项内容
2. 第二项内容
3. 第三项内容"""

        generated_html = self.processor.process_content(markdown_input)
        
        expected_html = """<ol>
  <li>第一项内容</li>
  <li>第二项内容</li>
  <li>第三项内容</li>
</ol>"""

        self.assertEqual(generated_html.strip(), expected_html.strip())

    def test_unordered_list_with_nested_items(self):
        """测试无序列表的嵌套结构"""
        markdown_input = """- **主要发现**
   - 子发现1
   - 子发现2
- **次要发现**
   - 子发现3
   - 子发现4"""

        generated_html = self.processor.process_content(markdown_input)
        
        # 验证无序列表嵌套结构
        self.assertIn('<ul>', generated_html)
        self.assertEqual(generated_html.count('<ul>'), 3)  # 1个主列表 + 2个嵌套列表
        self.assertIn('<strong>主要发现</strong>', generated_html)
        self.assertIn('<strong>次要发现</strong>', generated_html)

    def test_empty_and_edge_cases(self):
        """测试边界情况"""
        # 测试空内容
        self.assertEqual(self.processor.process_content(""), "<p>暂无内容</p>")
        
        # 测试只有主项没有子项
        markdown_input = "1. **只有主项**"
        generated_html = self.processor.process_content(markdown_input)
        expected_html = """<ol>
  <li><strong>只有主项</strong></li>
</ol>"""
        self.assertEqual(generated_html.strip(), expected_html.strip())

    def test_indentation_detection(self):
        """测试缩进检测功能"""
        # 测试空格缩进
        self.assertTrue(self.processor._is_indented_line("   - 缩进项"))
        self.assertTrue(self.processor._is_indented_line("    内容"))
        
        # 测试制表符缩进
        self.assertTrue(self.processor._is_indented_line("\t- 制表符缩进"))
        
        # 测试非缩进
        self.assertFalse(self.processor._is_indented_line("- 非缩进项"))
        self.assertFalse(self.processor._is_indented_line("1. 非缩进项"))

    def test_list_item_detection(self):
        """测试列表项检测功能"""
        # 测试有序列表
        self.assertTrue(self.processor._is_list_item("1. 有序列表项"))
        self.assertTrue(self.processor._is_list_item("99. 有序列表项"))
        
        # 测试无序列表
        self.assertTrue(self.processor._is_list_item("- 无序列表项"))
        self.assertTrue(self.processor._is_list_item("* 无序列表项"))
        self.assertTrue(self.processor._is_list_item("+ 无序列表项"))
        
        # 测试非列表项
        self.assertFalse(self.processor._is_list_item("普通文本"))
        self.assertFalse(self.processor._is_list_item("## 标题"))

    def test_complex_nested_scenario(self):
        """测试复杂嵌套场景（实际使用场景）"""
        markdown_input = """## 推断过程

1. **物理机分布核查**
   - 检查所有12个实例对应的物理机IP地址，确认每个实例均运行在独立的 NC 上。
   - 发现各 NC 在目标时间段内无异常记录。
   - **结论**：排除物理机层面聚集性故障的可能性。

2. **实例可用性诊断**
   - 对每个实例执行 `runVmUnavailableDiagnose` 工具，检查是否存在平台侧不可用原因。
   - 工具返回结果中未发现任何异常。
   - **结论**：未检测到由平台侧引发的 ECS 不可用问题。

3. **高危变更记录核查**
   - 使用 `listChangeRecords` 查询指定时间段内的变更记录。
   - 发现一次影响所有12个实例的高危变更：
     - 时间：2025-06-26 03:47:07
     - 描述：`vm_vsock_icmp_ping_loss_new` 异常（VSOCK_ICMP_PING 全丢包）
     - 服务名：`EcsVirtVMUpdateEngine.HotUpgradeCmd#`
     - 来源：tianji_vm
   - **结论**：此次变更是造成实例不可用现象的潜在根本原因。"""

        generated_html = self.processor.process_content(markdown_input)
        
        # 验证整体结构
        self.assertIn('<h2>推断过程</h2>', generated_html)
        self.assertEqual(generated_html.count('<ol>'), 1)  # 一个主有序列表
        self.assertEqual(generated_html.count('<ul>'), 3)  # 三个嵌套无序列表
        
        # 验证嵌套结构存在
        self.assertIn('    <ul>', generated_html)  # 应该有嵌套的 <ul>
        
        # 验证主要结构元素
        self.assertIn('<strong>物理机分布核查</strong>', generated_html)
        self.assertIn('<strong>实例可用性诊断</strong>', generated_html)
        self.assertIn('<strong>高危变更记录核查</strong>', generated_html)
        
        # 验证没有出现严重的层级拉平问题
        # 修复前：所有内容都是平铺的 <li>，没有嵌套结构
        # 修复后：应该有合理的嵌套结构，总 <li> 数量在合理范围内
        total_li_count = generated_html.count('<li>')
        self.assertGreaterEqual(total_li_count, 10)  # 至少10个列表项
        self.assertLessEqual(total_li_count, 20)  # 不超过20个列表项（合理范围）

    def test_complete_diagnostic_report_conversion(self):
        """测试完整诊断报告的Markdown到HTML转换"""
        markdown_input = """# 批量ECS实例不可用问题排查计划深度诊断报告

## 诊断信息
**实例ID**: i-t4n4vky24zw2w1qnqoyf, i-t4n74bsfzx58x0lj4qbh, i-t4na3cc0c9mimcw9667x, i-j6ch2zf4qfy1rltbql6r, i-2vc5alcmxz75rw8aol4g, i-2vc0zliaw8ilg744cdrq, i-2vc6qv34j96hkmcwms5d, i-2vcht9nfld3wrbv28qbs, i-j6c8465htk5rsyb13128, i-j6ch2zf4qfy1rltbql6p, i-j6ccxnd10yb809j28e8o, i-2vcglxct8xop32erzxgt  
**时间范围**: 2025-06-26 01:00:00 至 2025-06-26 04:00:00  
**问题描述**: **批量 ECS 实例发生不可用事件**

## 关键要点

- 所有12个实例分布在不同的物理机（NC）上，未发现**物理机层面的聚集性故障**。
- 使用 `runVmUnavailableDiagnose` 工具进行逐实例分析，未发现**平台侧导致的不可用原因**。
- 通过 `listChangeRecords` 查询到一次高危变更操作：服务 `EcsVirtVMUpdateEngine.HotUpgradeCmd#` 在 2025-06-26 03:47:07 引发**vm_vsock_icmp_ping_loss_new 异常**，表现为虚拟机 VSOCK_ICMP_PING 全丢包。
- 责任人涉及：078005、汤誓、徐云，来源为 tianji_vm。

## 推断过程

1. **物理机分布核查**
   - 检查所有12个实例对应的物理机IP地址，确认每个实例均运行在独立的 NC 上。
   - 发现各 NC 在目标时间段内无异常记录。
   - **结论**：排除物理机层面聚集性故障的可能性。

2. **实例可用性诊断**
   - 对每个实例执行 `runVmUnavailableDiagnose` 工具，检查是否存在平台侧不可用原因。
   - 工具返回结果中未发现任何异常。
   - **结论**：未检测到由平台侧引发的 ECS 不可用问题。

3. **高危变更记录核查**
   - 使用 `listChangeRecords` 查询指定时间段内的变更记录。
   - 发现一次影响所有12个实例的高危变更：
     - 时间：2025-06-26 03:47:07
     - 描述：`vm_vsock_icmp_ping_loss_new` 异常（VSOCK_ICMP_PING 全丢包）
     - 服务名：`EcsVirtVMUpdateEngine.HotUpgradeCmd#`
     - 来源：tianji_vm
   - **结论**：此次变更是造成实例不可用现象的潜在根本原因。

4. **网络通信能力评估**
   - 根据变更描述，该变更直接影响了虚拟机与宿主机之间的 VSOCK 网络通信。
   - VSOCK_ICMP_PING 是用于监控虚拟机健康状态的重要机制之一。
   - **结论**：此次变更有高度可能导致用户感知的"实例不可用"问题。

## 总结及建议

综合各项诊断结果：

1. 所有12个实例分布在不同的物理机上，且 NC 层面无异常；
2. 平台侧未发现导致实例不可用的直接原因；
3. 在目标时间段内存在一次影响全部实例的高危变更，明确描述了网络通信异常（VSOCK_ICMP_PING 全丢包）；
4. 变更时间为 2025-06-26 03:47:07，处于问题发生窗口期内，具有强相关性。

### **建议后续措施：**

1. **我们将进一步调查变更内容**：深入分析 `EcsVirtVMUpdateEngine.HotUpgradeCmd#` 的升级逻辑和变更影响范围。
2. **复盘变更流程与灰度控制策略**：建议优化热升级流程，确保关键路径变更具备更严格的灰度发布机制。
3. **提供受影响实例的网络监控日志**：运维团队将协助提取 VSOCK 通信异常期间的详细网络行为数据。
4. **建议回滚或修复该变更**：若确认为变更引入的问题，应尽快制定修复方案或回滚策略以避免再次发生。
5. **加强变更前的影响评估与测试覆盖**：建议对类似 tianji_vm 触发的变更增加预上线验证环节。"""

        generated_html = self.processor.process_content(markdown_input)
        
        # 验证主要结构元素
        self.assertIn('<h1>批量ECS实例不可用问题排查计划深度诊断报告</h1>', generated_html)
        self.assertIn('<h2>诊断信息</h2>', generated_html)
        self.assertIn('<h2>关键要点</h2>', generated_html)
        self.assertIn('<h2>推断过程</h2>', generated_html)
        self.assertIn('<h2>总结及建议</h2>', generated_html)
        self.assertIn('<h3><strong>建议后续措施：</strong></h3>', generated_html)
        
        # 验证粗体格式化
        self.assertIn('<strong>实例ID</strong>', generated_html)
        self.assertIn('<strong>时间范围</strong>', generated_html)
        self.assertIn('<strong>问题描述</strong>', generated_html)
        self.assertIn('<strong>批量 ECS 实例发生不可用事件</strong>', generated_html)
        
        # 验证行内代码格式化
        self.assertIn('<code>runVmUnavailableDiagnose</code>', generated_html)
        self.assertIn('<code>listChangeRecords</code>', generated_html)
        self.assertIn('<code>EcsVirtVMUpdateEngine.HotUpgradeCmd#</code>', generated_html)
        self.assertIn('<code>vm_vsock_icmp_ping_loss_new</code>', generated_html)
        
        # 验证嵌套列表结构
        self.assertEqual(generated_html.count('<ol>'), 3)  # 推断过程的有序列表 + 总结的有序列表 + 建议措施的有序列表
        self.assertGreaterEqual(generated_html.count('<ul>'), 5)  # 关键要点的无序列表 + 嵌套的子列表
        
        # 验证嵌套结构存在
        self.assertIn('    <ul>', generated_html)  # 应该有嵌套的 <ul>
        
        # 验证关键内容存在
        self.assertIn('物理机分布核查', generated_html)
        self.assertIn('实例可用性诊断', generated_html)
        self.assertIn('高危变更记录核查', generated_html)
        self.assertIn('网络通信能力评估', generated_html)
        self.assertIn('我们将进一步调查变更内容', generated_html)
        self.assertIn('运维团队将协助', generated_html)
        
        # 验证没有出现严重的结构问题
        total_li_count = generated_html.count('<li>')
        self.assertGreaterEqual(total_li_count, 15)  # 至少15个列表项
        self.assertLessEqual(total_li_count, 35)  # 不超过35个列表项（合理范围）
        
        # 验证专业表述（确保没有"联系技术支持"等不专业表述）
        self.assertNotIn('联系技术支持', generated_html)
        self.assertNotIn('咨询客服', generated_html)
        self.assertIn('我们将', generated_html)
        self.assertIn('运维团队', generated_html)


if __name__ == '__main__':
    unittest.main()
