import json
import os
from typing import Dict, Any

import httpx
import pytest

os.environ.setdefault('APP_ENV', 'test')


class MCPAPITester:
    """MCP API测试器"""
    
    def __init__(self, base_url: str = "http://pre-ecs-deep-diagnose.aliyun-inc.com", token: str = None):
        self.base_url = base_url
        self.token = token
        self.headers = {"Content-Type": "application/json"}
        if token:
            self.headers["Authorization"] = f"Bearer {token}"
    
    async def get_auth_token(self) -> str:
        """获取认证token"""
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{self.base_url}/api/token",
                json={
                    "access_key": "admin",
                    "secret_key": "admin",
                    "token_lifetime_minutes": 60
                }
            )
            if response.status_code == 200:
                data = response.json()
                return data.get("access_token")
            else:
                raise Exception(f"Failed to get token: {response.status_code} - {response.text}")
    
    async def get_mcp_tools(self) -> Dict[str, Any]:
        """获取MCP工具"""
        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                response = await client.get(
                    f"{self.base_url}/api/mcp/tools",
                    headers=self.headers
                )
                return {
                    "status_code": response.status_code,
                    "data": response.json() if response.status_code < 400 and response.content else None,
                    "error": response.text if response.status_code >= 400 else None
                }
            except Exception as e:
                return {
                    "status_code": 0,
                    "data": None,
                    "error": str(e)
                }
    
    async def refresh_mcp_tools(self) -> Dict[str, Any]:
        """刷新MCP工具缓存"""
        async with httpx.AsyncClient(timeout=60.0) as client:  # 刷新可能需要更长时间
            try:
                response = await client.post(
                    f"{self.base_url}/api/mcp/tools/refresh",
                    headers=self.headers
                )
                return {
                    "status_code": response.status_code,
                    "data": response.json() if response.status_code < 400 and response.content else None,
                    "error": response.text if response.status_code >= 400 else None
                }
            except Exception as e:
                return {
                    "status_code": 0,
                    "data": None,
                    "error": str(e)
                }
    
    async def get_mcp_tools_description(self) -> Dict[str, Any]:
        """获取MCP工具描述"""
        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                response = await client.get(
                    f"{self.base_url}/api/mcp/tools/description",
                    headers=self.headers
                )
                return {
                    "status_code": response.status_code,
                    "data": response.json() if response.status_code < 400 and response.content else None,
                    "error": response.text if response.status_code >= 400 else None
                }
            except Exception as e:
                return {
                    "status_code": 0,
                    "data": None,
                    "error": str(e)
                }


@pytest.mark.asyncio
async def test_mcp_api_get_tools():
    """测试获取MCP工具API"""
    tester = MCPAPITester()
    
    # 获取认证token
    token = await tester.get_auth_token()
    assert token is not None, "Failed to get auth token"
    
    # 设置token
    tester.token = token
    tester.headers["Authorization"] = f"Bearer {token}"
    
    # 测试获取MCP工具
    result = await tester.get_mcp_tools()
    
    print(f"Status Code: {result['status_code']}")
    if result['data']:
        print(f"Response Data: {json.dumps(result['data'], indent=2, ensure_ascii=False)}")
    if result['error']:
        print(f"Error: {result['error']}")
    
    # 验证响应
    if result["status_code"] == 0:
        pytest.fail(f"Network error: {result['error']}")
    
    assert result["status_code"] == 200, f"Expected 200, got {result['status_code']}. Error: {result.get('error', 'No error info')}"
    assert result["data"] is not None, "Response data is None"
    assert result["data"]["success"] is True, f"API call was not successful. Error: {result['data'].get('error', 'No error info')}"
    
    # 验证数据结构
    data = result["data"]["data"]
    assert "servers" in data, "Missing 'servers' in response"
    assert "total_servers" in data, "Missing 'total_servers' in response"
    assert "total_tools" in data, "Missing 'total_tools' in response"
    
    print(f"✅ Found {data['total_tools']} tools from {data['total_servers']} servers")


@pytest.mark.asyncio
async def test_mcp_api_refresh_tools():
    """测试刷新MCP工具缓存API"""
    tester = MCPAPITester()
    
    # 获取认证token
    token = await tester.get_auth_token()
    assert token is not None, "Failed to get auth token"
    
    # 设置token
    tester.token = token
    tester.headers["Authorization"] = f"Bearer {token}"
    
    # 测试刷新MCP工具缓存
    result = await tester.refresh_mcp_tools()
    
    print(f"Status Code: {result['status_code']}")
    if result['data']:
        print(f"Response Data: {json.dumps(result['data'], indent=2, ensure_ascii=False)}")
    if result['error']:
        print(f"Error: {result['error']}")
    
    # 验证响应
    if result["status_code"] == 0:
        pytest.fail(f"Network error: {result['error']}")
    
    assert result["status_code"] == 200, f"Expected 200, got {result['status_code']}. Error: {result.get('error', 'No error info')}"
    assert result["data"] is not None, "Response data is None"
    assert result["data"]["success"] is True, f"API call was not successful. Error: {result['data'].get('error', 'No error info')}"
    
    # 验证数据结构
    assert "message" in result["data"], "Missing 'message' in response"
    assert "data" in result["data"], "Missing 'data' in response"
    
    data = result["data"]["data"]
    assert "total_servers" in data, "Missing 'total_servers' in response"
    assert "total_tools" in data, "Missing 'total_tools' in response"
    assert "servers" in data, "Missing 'servers' in response"
    
    print(f"✅ Refreshed {data['total_tools']} tools from {data['total_servers']} servers")


@pytest.mark.asyncio
async def test_mcp_api_get_tools_description():
    """测试获取MCP工具描述API"""
    tester = MCPAPITester()
    
    # 获取认证token
    token = await tester.get_auth_token()
    assert token is not None, "Failed to get auth token"
    
    # 设置token
    tester.token = token
    tester.headers["Authorization"] = f"Bearer {token}"
    
    # 测试获取MCP工具描述
    result = await tester.get_mcp_tools_description()
    
    print(f"Status Code: {result['status_code']}")
    if result['data']:
        print(f"Response Data Keys: {list(result['data'].keys())}")
        if result['data'].get('data') and result['data']['data'].get('description'):
            description = result['data']['data']['description']
            print(f"Description Length: {len(description)} characters")
            print(f"Description Preview: {description[:200]}...")
    if result['error']:
        print(f"Error: {result['error']}")
    
    # 验证响应
    if result["status_code"] == 0:
        pytest.fail(f"Network error: {result['error']}")
    
    assert result["status_code"] == 200, f"Expected 200, got {result['status_code']}. Error: {result.get('error', 'No error info')}"
    assert result["data"] is not None, "Response data is None"
    assert result["data"]["success"] is True, f"API call was not successful. Error: {result['data'].get('error', 'No error info')}"
    
    # 验证数据结构
    assert "data" in result["data"], "Missing 'data' in response"
    data = result["data"]["data"]
    assert "description" in data, "Missing 'description' in response"
    assert "format" in data, "Missing 'format' in response"
    assert data["format"] == "markdown", "Expected markdown format"
    
    print(f"✅ Got tools description with {len(data['description'])} characters")


@pytest.mark.asyncio
async def test_mcp_api_full_workflow():
    """测试MCP API完整工作流程"""
    tester = MCPAPITester()
    
    # 获取认证token
    token = await tester.get_auth_token()
    assert token is not None, "Failed to get auth token"
    
    # 设置token
    tester.token = token
    tester.headers["Authorization"] = f"Bearer {token}"
    
    print("🔄 Starting MCP API workflow test...")
    
    # 1. 刷新工具缓存
    print("1️⃣ Refreshing tools cache...")
    refresh_result = await tester.refresh_mcp_tools()
    if refresh_result["status_code"] == 0:
        pytest.fail(f"Network error during refresh: {refresh_result['error']}")
    assert refresh_result["status_code"] == 200, f"Failed to refresh tools: {refresh_result.get('error', 'No error info')}"
    assert refresh_result["data"]["success"] is True, f"Refresh was not successful: {refresh_result['data'].get('error', 'No error info')}"
    
    # 2. 获取工具列表
    print("2️⃣ Getting tools list...")
    tools_result = await tester.get_mcp_tools()
    if tools_result["status_code"] == 0:
        pytest.fail(f"Network error during get tools: {tools_result['error']}")
    assert tools_result["status_code"] == 200, f"Failed to get tools: {tools_result.get('error', 'No error info')}"
    assert tools_result["data"]["success"] is True, f"Get tools was not successful: {tools_result['data'].get('error', 'No error info')}"
    
    # 3. 获取工具描述
    print("3️⃣ Getting tools description...")
    desc_result = await tester.get_mcp_tools_description()
    if desc_result["status_code"] == 0:
        pytest.fail(f"Network error during get description: {desc_result['error']}")
    assert desc_result["status_code"] == 200, f"Failed to get description: {desc_result.get('error', 'No error info')}"
    assert desc_result["data"]["success"] is True, f"Get description was not successful: {desc_result['data'].get('error', 'No error info')}"
    
    # 验证数据一致性
    tools_count = tools_result["data"]["data"]["total_tools"]
    refresh_count = refresh_result["data"]["data"]["total_tools"]
    
    print(f"📊 Tools count consistency check:")
    print(f"   - Refresh result: {refresh_count} tools")
    print(f"   - Get tools result: {tools_count} tools")
    
    # 工具数量应该一致（允许小的差异，因为可能有时间差）
    assert abs(tools_count - refresh_count) <= 1, f"Tool count mismatch: {tools_count} vs {refresh_count}"
    
    print("✅ MCP API workflow test completed successfully!")


if __name__ == "__main__":
    # 可以直接运行单个测试
    import asyncio
    asyncio.run(test_mcp_api_get_tools())