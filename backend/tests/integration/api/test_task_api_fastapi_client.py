import os
import json
import uuid

import pytest
import httpx
from fastapi import status
from fastapi.testclient import TestClient

os.environ.setdefault('APP_ENV', 'test')


class TestTaskAPIFastAPIClient:
    """测试任务API的集成功能"""

    @pytest.fixture(scope="class")
    def client(self):
        """为所有测试提供一个 FastAPI TestClient 实例，并处理认证"""
        from deep_diagnose.api.app import app
        test_client = TestClient(app)
        
        # 获取认证token
        auth_data = {
            "access_key": "admin",
            "secret_key": "admin",
            "token_lifetime_minutes": 60
        }
        
        token_response = test_client.post("/api/token", json=auth_data)
        assert token_response.status_code == 200
        token_content = token_response.json()
        access_token = token_content.get("access_token")
        assert access_token is not None
        
        # 将认证头添加到客户端
        test_client.headers.update({"Authorization": f"Bearer {access_token}"})
        
        return test_client

    def test_create_task_returns_202_on_success(self, client: TestClient):
        """测试成功创建任务时返回202状态码"""
        # Arrange
        request_payload = {
            "agent": "DiagnoseAgent",
            "question": "测试问题"
        }

        # Act
        response = client.post(
            "/api/v1/tasks",
            json=request_payload
        )

        # Assert
        assert response.status_code == status.HTTP_202_ACCEPTED
        data = response.json()
        assert "task_id" in data
        assert data["task_id"] is not None

    def test_create_task_returns_400_for_empty_agent(self, client: TestClient):
        """测试创建任务时agent为空时返回400状态码"""
        # Arrange
        request_payload = {
            "agent": "",
            "question": "测试问题"
        }

        # Act
        response = client.post(
            "/api/v1/tasks",
            json=request_payload
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        data = response.json()
        assert "agent字段不能为空" in data["detail"]
    
    def test_create_task_returns_400_for_empty_question(self, client: TestClient):
        """测试创建任务时question为空时返回400状态码"""
        # Arrange
        request_payload = {
            "agent": "DiagnoseAgent",
            "question": ""
        }

        # Act
        response = client.post(
            "/api/v1/tasks",
            json=request_payload
        )

        # Assert
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        data = response.json()
        assert "question字段不能为空" in data["detail"]
    
    def test_create_task_returns_422_for_missing_fields(self, client: TestClient):
        """测试创建任务时缺少必填字段时返回422状态码"""
        # Arrange - Missing agent field
        request_payload_missing_agent = {"question": "测试问题"}

        # Act - Missing agent field
        response_missing_agent = client.post(
            "/api/v1/tasks",
            json=request_payload_missing_agent
        )

        # Assert - Missing agent field
        assert response_missing_agent.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

        # Arrange - Missing question field
        request_payload_missing_question = {"agent": "DiagnoseAgent"}

        # Act - Missing question field
        response_missing_question = client.post(
            "/api/v1/tasks",
            json=request_payload_missing_question
        )

        # Assert - Missing question field
        assert response_missing_question.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    def test_get_task_status_returns_200_on_success(self, client: TestClient):
        """测试成功获取任务状态时返回200状态码"""
        # Arrange: Create a task to get a valid task_id
        create_response = client.post(
            "/api/v1/tasks",
            json={
                "agent": "DiagnoseAgent",
                "question": "测试获取任务状态"
            }
        )
        assert create_response.status_code == status.HTTP_202_ACCEPTED
        task_id = create_response.json()["task_id"]

        # Act: Get the status of the created task
        response = client.get(f"/api/v1/tasks/{task_id}")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["task_id"] == task_id
        assert data["status"] == "PROCESSING" # Newly created tasks are PROCESSING
        assert data["data"] is None # No data yet for PENDING task

    def test_get_task_status_returns_404_for_not_found(self, client: TestClient):
        """测试获取不存在的任务状态时返回404状态码"""
        # Arrange
        non_existent_task_id = "non-existent-task"

        # Act
        response = client.get(f"/api/v1/tasks/{non_existent_task_id}")

        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "不存在或已过期" in data["detail"]
    
    def test_get_task_status_returns_4xx_for_empty_id(self, client: TestClient):
        """测试获取任务状态时ID为空时返回4xx状态码"""
        # Arrange
        empty_task_id = " "  # 空格会被trim

        # Act
        response = client.get(f"/api/v1/tasks/{empty_task_id}")

        # Assert
        assert response.status_code in [status.HTTP_404_NOT_FOUND, status.HTTP_400_BAD_REQUEST]
    
    def test_get_simplified_task_status_returns_200_on_success(self, client: TestClient):
        """测试简化状态查询成功时返回200状态码"""
        # Arrange: Create a task to get a valid task_id
        create_response = client.post(
            "/api/v1/tasks",
            json={
                "agent": "DiagnoseAgent",
                "question": "测试简化状态查询"
            }
        )
        assert create_response.status_code == status.HTTP_202_ACCEPTED
        task_id = create_response.json()["task_id"]

        # Act: Get the simplified status of the created task
        response = client.get(f"/api/v1/tasks/{task_id}/status")

        # Assert
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data["task_id"] == task_id
        assert data["status"] == "PROCESSING"
        assert data["submitted_at"] is not None
        assert data["completed_at"] is None
        assert "data" not in data
    
    def test_get_simplified_task_status_returns_404_for_not_found(self, client: TestClient):
        """测试简化状态查询任务不存在时返回404状态码"""
        # Arrange
        non_existent_task_id = "non-existent-task"

        # Act
        response = client.get(f"/api/v1/tasks/{non_existent_task_id}/status")

        # Assert
        assert response.status_code == status.HTTP_404_NOT_FOUND
        data = response.json()
        assert "error" in data
        assert "不存在或已过期" in data["error"]
