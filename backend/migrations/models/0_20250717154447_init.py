from tortoise import BaseDBAsync<PERSON>lient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `cloudbot_agent_user` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
    `username` VARCHAR(64) NOT NULL UNIQUE COMMENT '用户名/员工工号',
    `password` VARCHAR(255) NOT NULL COMMENT '哈希后的密码',
    `gmt_create` DATETIME(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6),
    `gmt_modified` DATETIME(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
) CHARACTER SET utf8mb4 COMMENT='用户信息表';
CREATE TABLE IF NOT EXISTS `cloudbot_agent_chat_message` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
    `session_id` VARCHAR(64) NOT NULL COMMENT '会话id',
    `message` LONGTEXT NOT NULL COMMENT '聊天信息',
    `message_type` VARCHAR(16) NOT NULL COMMENT '发送者，ai:机器人回复，human:人工回复' DEFAULT '',
    `scenario` VARCHAR(16) NOT NULL COMMENT '场景编码' DEFAULT '',
    `source` INT NOT NULL COMMENT '来源，0:pc端机器人会话，1:全链路定界，2:钉钉对话' DEFAULT 0,
    `gmt_create` DATETIME(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6),
    `gmt_modified` DATETIME(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `message_id` BIGINT NOT NULL COMMENT '问题ID' DEFAULT 0,
    `status` SMALLINT NOT NULL COMMENT '是否完成请求，0:已完成，1未完成' DEFAULT 0,
    KEY `idx_cloudbot_ag_session_93e2c9` (`session_id`),
    KEY `idx_cloudbot_ag_message_c1cf50` (`message_id`),
    KEY `idx_cloudbot_ag_status_0f9fe6` (`status`, `gmt_create`)
) CHARACTER SET utf8mb4 COMMENT='机器人聊天记录表';
CREATE TABLE IF NOT EXISTS `cloudbot_agent_chat_session` (
    `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT COMMENT '自增主键',
    `session_id` VARCHAR(64) NOT NULL UNIQUE COMMENT '会话id',
    `user_id` VARCHAR(16) NOT NULL COMMENT '员工工号',
    `title` VARCHAR(512) NOT NULL COMMENT '会话标题' DEFAULT '',
    `gmt_create` DATETIME(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6),
    `gmt_modified` DATETIME(6) NOT NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
    `ext` VARCHAR(64) NOT NULL COMMENT '扩展字段' DEFAULT '',
    `instance_id` VARCHAR(64) NOT NULL COMMENT '诊断实例id' DEFAULT '',
    KEY `idx_cloudbot_ag_user_id_2b410f` (`user_id`)
) CHARACTER SET utf8mb4 COMMENT='机器人会话表';
CREATE TABLE IF NOT EXISTS `aerich` (
    `id` INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
    `version` VARCHAR(255) NOT NULL,
    `app` VARCHAR(100) NOT NULL,
    `content` JSON NOT NULL
) CHARACTER SET utf8mb4;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
