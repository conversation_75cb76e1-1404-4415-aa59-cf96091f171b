# MCP模块使用指南（Q&A）


### **Q: 什么是MCP工具？**
A: MCP（Model Context Protocol）是 Anthropic 推出的开放协议，让大型语言模型（LLM）与外部工具和数据源实现无缝通信。采用客户端–服务器架构，通过标准化接口，让LLM应用能安全、高效地连接到各种数据源和工具。


### **Q: MCP模块的主要组件有哪些？**
A: MCP模块主要包括以下四个核心组件：
1. **MCPToolManager**：统一管理器，提供对外的接口。
2. **MCPCacheManager**：缓存管理器，负责从Redis中读取和写入工具信息。
3. **MCPToolClient**：客户端，与MCP服务器通信获取工具。
4. **MCPToolConfig**：配置管理器，处理服务器和工具的配置信息。



### **Q: 如何新增MCP工具配置？**
A: 在 [config_daily.yaml](..%2F..%2Fbackend%2Fsrc%2Fdeep_diagnose%2Fcommon%2Fconfig%2Ffiles%2Fconfig_daily.yaml) 和[config_prod.yaml](..%2F..%2Fbackend%2Fsrc%2Fdeep_diagnose%2Fcommon%2Fconfig%2Ffiles%2Fconfig_prod.yaml)中添加相关配置：
参考如下内容：
```yaml
  vm_coredump: # 工具组名称
    protocol: streamable_http # 协议类型
    base_url: https://ecs-mcp.alibaba-inc.com # 服务器地址
    path: /vm_coredump/mcp/ # 路径
    token: ************************************************ # 认证信息
    auth: token # 认证方式
    enabled_tools: # 启用的工具列表
      - get_vm_coredump # 工具名称
```
![mcp_servers.png](..%2Fassets%2Fpng%2Fmcp_servers.png)



### **Q: 如何获取所有启用的MCP工具？**
A: 调用 `get_enabled_mcp_tools()` 方法来获取所有启用的工具：
```python
tool_manager = MCPToolManager()
tools = await tool_manager.get_enabled_mcp_tools()
```


### **Q: 如何生成工具描述文档？**
A: 使用 `get_enabled_mcp_tools_description()` 方法生成工具描述文档，可用于展示或自动生成API说明：
```python
tool_manager = MCPToolManager()
tools_description = await tool_manager.get_enabled_mcp_tools_description()
```
