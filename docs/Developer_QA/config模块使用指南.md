# 配置管理模块使用指南（Q&A）


### **Q: 什么是配置管理模块？**
A: 配置管理模块是一个用于统一管理应用配置的系统，支持从YAML文件、环境变量等多种来源加载配置，并提供安全配置管理、多环境支持等功能。


### **Q: 配置管理模块的主要组件有哪些？**
A: 配置管理模块主要包括以下核心组件：
1. **BaseConfig**：抽象基类，定义基础配置操作（如加载、获取、重载）。
2. **YamlConfigLoader**：实现YAML配置文件的加载与处理。
3. **EnvConfigLoader**：实现环境变量的配置加载与类型转换。
4. **SecurityConfig**：安全管理器，负责敏感信息的加密/解密。
5. **KeyCenterManager**：密钥管理中心，提供实际的加解密能力。


### **Q: 如何添加配置文件？**
A: 在 [config_daily.yaml](..%2F..%2Fbackend%2Fsrc%2Fdeep_diagnose%2Fcommon%2Fconfig%2Ffiles%2Fconfig_daily.yaml) 和[config_prod.yaml](..%2F..%2Fbackend%2Fsrc%2Fdeep_diagnose%2Fcommon%2Fconfig%2Ffiles%2Fconfig_prod.yaml)中添加相关配置



### **Q: 如何访问具体的配置项？**
A: 首先获取配置对象，然后通过点符号访问配置项，例如：
```python
from deep_diagnose.common.config import get_config
config = get_config()
app_name = config.app.name
redis_host = config.infrastructure.redis.host
oss_endpoint = config.observability.oss.endpoint
```



### **Q: 如何加密敏感数据？**
A: keycenter中进行加密
https://kc-cn.alibaba-inc.com/index?spm=a1z3jw.map.0.0.5e8963ccZaifTg#/key/list?activeKey=all&name=ecs-deep-diagnose_aone_key



### **Q: 如何添加敏感数据？**
A: 参考示例：qwen_api_key 
&qwen_ak 为加密后的key
![img.png](../assets/png/security.png)
在这里增加自己的敏感数据
如下如所示配置即可 在使用的时候 !decrypt [ *qwen_ak, *keycenter_pub_name ] 即可
![img.png](../assets/png/use_security.png)



### **Q: 如何切换不同的环境配置？**
A: 使用 如下方式 来检测当前环境，并根据需要加载对应的配置文件：
```python
from deep_diagnose.common.config.core.environment import get_environment, is_production_environment

current_env = get_environment()
is_prod = is_production_environment(current_env)

from deep_diagnose.common.config.core.environment import get_config_file_name
config_file = get_config_file_name(current_env)
```



### **Q: 如何使用内置常量？**
A: 可以直接从 `constants` 模块导入所需的常量，例如：
```python
from deep_diagnose.common.config.constants.questions import BUILT_IN_QUESTIONS_ZH_CN
from deep_diagnose.common.config.constants.tools import SELECTED_SEARCH_ENGINE
```


### **Q: 如何调试配置相关的问题？**
A: 如果遇到配置相关的问题，可以通过以下步骤进行排查：
1. 确认 YAML 文件路径是否正确，文件内容格式是否无误。
2. 检查环境变量是否设置正确，特别是前缀是否匹配。
3. 如果涉及加密数据，确保 KeyCenter 的密钥和权限配置正确。
