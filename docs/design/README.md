# ECS 深度诊断系统 - 后端设计文档 (V2)

## 引言

本文档是 **ECS 深度诊断系统** 后端服务的核心设计与实现说明。V2 版本旨在提供一个逻辑严密、图文并茂、与当前代码实现高度一致的权威参考。

本文档为所有开发者、架构师和运维人员提供系统内部工作原理的深度洞察，确保团队对系统有统一和清晰的理解，并为未来的功能迭代和维护提供坚实的基础。

---

## 文档结构

本设计文档采用分层、多文件的结构，通过以下章节全面阐述系统设计的各个方面。请循序阅读，以建立对系统的完整认知。

*   **[第一章：系统概述](./01_系统概述.md)**
    *   介绍项目的核心价值、目标用户以及解决的关键问题。

*   **[第二章：架构设计](./02_架构设计.md)**
    *   宏观展示系统的分层架构、核心组件以及它们之间的关系。

*   **[第三章：核心工作流](./03_核心工作流.md)**
    *   详细拆解基于 LangGraph 的多智能体协作流程，揭示诊断任务的完整生命周期。

*   **[第四章：API 接口规范](./04_API接口规范.md)**
    *   定义所有对外暴露的 RESTful API，包括异步任务接口和实时通信接口。

*   **[第五章：配置与安全](./05_配置与安全.md)**
    *   深入解析分层配置系统和密钥管理机制，确保系统的灵活性和安全性。

*   **[第六章：服务模块详解](./06_服务模块/README.md)**
    *   对后端的核心服务模块进行详细说明，包括任务服务、报告生成和 SSE 解析等。

*   **[第七章：代码结构与规范](./07_代码结构与规范.md)**
    *   提供后端源码的目录结构导览和核心开发规范。
