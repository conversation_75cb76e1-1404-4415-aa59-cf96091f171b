# 第七章：代码结构与规范

## 7.1 后端源码目录结构

后端服务代码位于 `backend/src/` 目录下，其核心逻辑封装在 `deep_diagnose` 包中。以下是 `deep_diagnose` 包的详细目录结构及其职责。

```
backend/src/deep_diagnose/
├── api/          # API 接口层：FastAPI 应用、路由、中间件、请求/响应模型
│   ├── middleware/ # 中间件，如认证、CORS
│   ├── model/      # API 内部使用的数据模型（如 Pydantic 模型）
│   ├── models/     # 对外暴露的请求/响应数据模型
│   └── routes/     # 各业务模块的 API 路由定义
├── common/       # 通用模块：配置系统、常量、异常定义、通用工具函数、类型定义
│   ├── config/     # 配置管理系统
│   ├── constants/  # 全局常量定义
│   ├── exceptions/ # 自定义异常类
│   ├── types/      # 通用类型定义
│   └── utils/      # 通用工具函数
├── core/         # 核心业务逻辑层：多智能体工作流、智能体实现、规划逻辑
│   ├── agents/     # 各智能体的具体实现（如 CoordinatorAgent, PlannerAgent）
│   ├── planning/   # 规划相关逻辑，如 SOP 管理、策略选择
│   └── workflow/   # LangGraph 工作流的构建和节点定义
├── llms/         # 大语言模型集成：LLM 客户端、模型抽象、LLM 调用封装
├── logs/         # 日志文件输出目录
├── prompts/      # 提示词管理：各智能体的提示词模板、SOP 内容
│   └── operation_sop/ # 标准操作程序 (SOP) 的 Markdown 文件
├── security/     # 安全模块：认证、授权、密钥中心集成
│   ├── auth/       # 认证逻辑，如 JWT 验证
│   ├── buc/        # BUC SSO 集成
│   └── keycenter/  # 密钥中心集成
├── services/     # 业务服务层：封装特定业务逻辑的服务，如任务管理、报告生成、SSE 处理
│   ├── report_generator/ # 报告生成服务
│   ├── sse/              # SSE 消息解析服务
│   └── task/             # 异步任务管理服务
├── storage/      # 存储层：Redis 客户端、OSS 客户端等存储相关封装
│   ├── oss_client.py
│   └── redis_client.py
└── tools/        # 工具层：智能体可调用的外部工具封装，如 MCP 工具、搜索引擎、Python REPL
    ├── mcp_tools.py
    ├── python_repl.py
    └── search.py
```

## 7.2 开发规范

### 7.2.1 命名规范

*   **模块/文件**：小写，下划线分隔 (snake_case)，如 `task_service.py`。
*   **类名**：驼峰命名 (CamelCase)，如 `TaskService`。
*   **函数/方法/变量**：小写，下划线分隔 (snake_case)，如 `create_task`。
*   **常量**：全大写，下划线分隔 (UPPER_SNAKE_CASE)，如 `MAX_RETRIES`。

### 7.2.2 代码风格

*   遵循 **PEP 8** 规范。
*   使用 **Black** 进行代码格式化，确保代码风格一致性。
*   使用 **isort** 自动排序导入。
*   使用 **ruff** 进行 Linting 和静态分析。

### 7.2.3 类型提示 (Type Hinting)

*   所有函数参数和返回值都应使用类型提示，提高代码可读性和可维护性。
*   复杂数据结构使用 `typing` 模块中的类型，如 `List`, `Dict`, `Optional`。

### 7.2.4 日志规范

*   使用 Python 标准库 `logging` 进行日志记录。
*   日志级别：`DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL`。
*   生产环境建议使用 `INFO` 及以上级别。
*   敏感信息（如用户密码、API Key）严禁出现在日志中。
*   使用结构化日志，便于日志分析和检索。

### 7.2.5 错误处理

*   使用 Python 的异常机制进行错误处理。
*   对于可预期的业务错误，定义自定义异常类。
*   在 API 接口层，将内部异常转换为标准的 HTTP 错误响应。

### 7.2.6 文档字符串 (Docstrings)

*   所有模块、类、函数和方法都应包含清晰、简洁的文档字符串，解释其功能、参数、返回值和可能抛出的异常。
*   推荐使用 Google 风格的 Docstrings。

### 7.2.7 测试

*   编写单元测试、集成测试和端到端测试，确保代码质量和功能正确性。
*   使用 `pytest` 作为测试框架。
*   测试文件应与被测试代码保持相同的目录结构，并以 `test_` 开头。
