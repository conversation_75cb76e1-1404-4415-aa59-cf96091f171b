# 6.2 报告生成器 (ReportGenerator)

## 6.2.1 概述

`ReportGenerator` 模块负责将诊断工作流的输出（通常是 Markdown 格式的文本）转换为用户友好的、结构化的 HTML 报告。它旨在提供高质量的报告渲染，解决传统 PDF 生成器在格式、样式和文件大小上的痛点。

## 6.2.2 核心职责

*   **内容转换**：将 Markdown 格式的诊断结果、过程明细等内容转换为 HTML 格式。
*   **样式渲染**：应用预定义的 CSS 样式，确保报告具有专业、统一的视觉效果，包括精确的空格控制、缩进显示和代码高亮。
*   **报告生成**：提供统一的接口，根据传入的诊断数据生成完整的 HTML 报告文件。
*   **文件管理**：负责生成报告文件的存储和旧文件的清理。

## 6.2.3 技术优势

本报告生成器特别强调了 HTML 方案的优势，以解决传统 PDF 生成器（如 `ReportLab`）的常见问题：

| 特性         | 传统 PDF 生成器 (ReportLab) | HTML 生成器 (本系统)       |
|--------------|-----------------------------|----------------------------|
| **空格控制** | ❌ 异常大空格               | ✅ 精确控制                |
| **缩进显示** | ❌ 缩进丢失                 | ✅ 完美显示                |
| **代码渲染** | ❌ 占位符泄露               | ✅ 正确渲染                |
| **文件大小** | 较大                        | 较小 (例如 125KB vs 15KB)  |
| **加载速度** | 慢                          | 快                         |
| **移动端支持** | 差                          | 优秀 (响应式设计)          |
| **调试难度** | 高                          | 低                         |
| **依赖复杂度** | 高                          | 低 (纯 HTML/CSS)           |

## 6.2.4 模块架构

报告生成器模块采用工厂模式和接口抽象，便于扩展和维护。

```mermaid
graph TD
    A[ReportGeneratorFactory] --> B(DocumentGenerator)
    B --> C(ContentProcessor)
    B --> D(StorageManager)

    subgraph Implementations
        C1[HTMLMarkdownProcessor]
        D1[LocalFileManager]
    end

    C --> C1
    D --> D1
```
*图 6-1 报告生成器模块架构*

*   **`ReportGeneratorFactory`**：工厂类，根据需求创建具体的报告生成器实例（如 `HTMLGenerator`）。
*   **`DocumentGenerator`**：核心接口，定义了生成报告（`generate_full_report`, `generate_simple_report`）和清理文件（`cleanup_old_files`）等通用方法。
*   **`ContentProcessor`**：接口，负责将原始内容（如 Markdown）转换为目标格式（如 HTML）。`HTMLMarkdownProcessor` 是其具体实现。
*   **`StorageManager`**：接口，负责文件的保存和管理。`LocalFileManager` 是其具体实现，负责本地文件系统的操作。

## 6.2.5 关键代码路径

*   **`backend/src/deep_diagnose/services/report_generator/`**：报告生成器模块的根目录。
*   **`backend/src/deep_diagnose/services/report_generator/factory.py`**：报告生成器工厂。
*   **`backend/src/deep_diagnose/services/report_generator/html_generator/`**：HTML 报告生成器的具体实现。
*   **`backend/src/deep_diagnose/services/report_generator/html_generator/content_processor.py`**：Markdown 到 HTML 的转换逻辑。
