# 6.1 任务服务 (TaskService)

## 6.1.1 概述

`TaskService` 是后端系统中负责管理异步诊断任务生命周期的核心服务。由于诊断任务可能耗时较长，系统采用异步处理模式，`TaskService` 负责任务的创建、状态更新、结果存储和查询。

## 6.1.2 核心职责

*   **任务创建**：接收来自 API 层的任务请求，生成唯一的 `task_id`，并在存储层（Redis）中初始化任务记录。
*   **状态管理**：维护任务的当前状态（`PENDING`, `PROCESSING`, `SUCCESS`, `FAILURE`），并提供更新状态的接口。
*   **结果存储**：将诊断工作流的中间过程和最终报告存储到任务记录中。
*   **任务查询**：提供根据 `task_id` 查询任务详细信息（包括状态、结果、错误信息）的接口。
*   **生命周期管理**：支持任务的过期机制（例如，任务数据在 Redis 中存储 24 小时后自动过期）。

## 6.1.3 技术实现

*   **存储层**：`TaskService` 主要依赖 **Redis** 作为其任务数据的持久化存储。Redis 的高性能读写能力非常适合存储频繁更新的任务状态和中间结果。
*   **数据模型**：定义了 `TaskRequest`, `TaskResponse`, `TaskStatusResponse`, `TaskData` 等数据模型，确保任务数据的结构化和一致性。
*   **异步操作**：所有与 Redis 的交互都采用异步（`async/await`）方式，避免阻塞主线程。

## 6.1.4 关键代码路径

*   **`backend/src/deep_diagnose/services/task/task_service.py`**：`TaskService` 的主要实现文件。
*   **`backend/src/deep_diagnose/api/routes/tasks.py`**：API 路由层调用 `TaskService` 的入口。
*   **`backend/src/deep_diagnose/storage/redis_client.py`**：`TaskService` 依赖的 Redis 客户端。
