# 第四章：API 接口规范

## 4.1 设计理念

API 是系统对外提供服务的窗口，其设计遵循 **RESTful** 最佳实践，并充分考虑了与前端及其他后端服务集成的便捷性与安全性。

*   **资源导向**：API 以资源为中心进行设计，如 `tasks`（任务）。
*   **协议与方法**：使用标准的 HTTP/HTTPS 协议和动词（`GET`, `POST`）。
*   **数据格式**：所有请求和响应体均使用 JSON 格式。
*   **安全性**：所有接口均通过 JWT (JSON Web Token) 进行认证和授权。
*   **异步优先**：对于耗时操作（如诊断任务），采用异步处理模式，避免客户端长时间等待。

## 4.2 异步任务接口

这是系统的核心业务接口，用于管理长时间运行的诊断任务。

### 4.2.1 异步处理流程

下图展示了客户端提交一个异步任务后，系统内部的处理流程。

![API 异步任务处理流程图](../assets/png/03_api_async_task_flow.png)
*图 4-1 API 异步任务处理流程图*

1.  **提交任务**：客户端向 `POST /api/v1/tasks` 发送请求，提交诊断问题。
2.  **接收与验证**：API 层的路由接收请求，并进行参数验证和权限校验。
3.  **创建任务记录**：`TaskService` 被调用，它在 Redis 中创建一条任务记录，状态为 `PENDING`，并生成一个唯一的 `task_id`。
4.  **立即响应**：API 层立即以 `202 Accepted` 状态码响应客户端，并在响应体中返回 `task_id`。
5.  **后台执行**：API 层通过 `asyncio.create_task()` 异步触发 `TaskExecutor` 开始在后台执行诊断工作流。
6.  **状态更新**：`TaskExecutor` 在执行过程中，会通过 `TaskService` 持续更新 Redis 中的任务状态（如 `PROCESSING`, `SUCCESS`, `FAILURE`）和结果。
7.  **轮询获取结果**：客户端可以使用 `task_id` 定期轮询 `GET /api/v1/tasks/{task_id}` 接口，以获取任务的最新状态和最终结果。

### 4.2.2 接口定义

#### 1. 提交诊断任务

*   **`POST /api/v1/tasks`**
*   **描述**：提交一个异步诊断任务。
*   **认证**：需要 JWT。
*   **请求体** (`application/json`)：
    ```json
    {
      "agent": "DiagnoseAgent",
      "question": "分析实例 i-xxx 的运维事件，判断其可能的故障原因。"
    }
    ```
*   **成功响应** (`202_ACCEPTED`)：
    ```json
    {
      "task_id": "a8c5d7e6-f4b3-4a21-8d9e-0c7b1a6f2e5d"
    }
    ```

#### 2. 查询任务状态及结果

*   **`GET /api/v1/tasks/{task_id}`**
*   **描述**：根据任务 ID 查询任务的完整状态和结果。
*   **认证**：需要 JWT。
*   **成功响应** (`200_OK`)：
    ```json
    {
      "task_id": "a8c5d7e6-f4b3-4a21-8d9e-0c7b1a6f2e5d",
      "status": "SUCCESS",
      "submitted_at": "2023-10-27T10:00:00Z",
      "completed_at": "2023-10-27T10:05:10Z",
      "data": {
        "result": "## 运维事件共性分析报告...",
        "detail": "## 执行过程...",
        "error": null
      }
    }
    ```

#### 3. 简化状态查询

*   **`GET /api/v1/tasks/{task_id}/status`**
*   **描述**：只返回任务的基本状态信息，用于快速轮询。
*   **认证**：需要 JWT。
*   **成功响应** (`200_OK`)：
    ```json
    {
      "task_id": "a8c5d7e6-f4b3-4a21-8d9e-0c7b1a6f2e5d",
      "status": "SUCCESS"
    }
    ```

## 4.3 实时通信接口

*   **`POST /api/chat/stream`**
*   **描述**：用于与前端进行实时、流式的交互。当用户在聊天界面中输入问题时，此接口被调用。
*   **协议**：**Server-Sent Events (SSE)**。后端会保持连接打开，并持续将工作流中产生的事件（如智能体的思考过程、工具的调用和结果）流式地推送给前端。
*   **优势**：相比 WebSocket，SSE 更轻量，且基于标准的 HTTP，易于实现和调试。它非常适合这种服务器到客户端的单向实时数据推送场景。
