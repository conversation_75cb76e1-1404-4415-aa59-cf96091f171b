<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="900" viewBox="0 0 1400 900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 现代渐变色彩定义 -->
    <linearGradient id="mcpGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="configGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="toolGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="promptGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="llmGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0891b2;stop-opacity:1" />
    </linearGradient>
    
    <!-- 柔和阴影滤镜 -->
    <filter id="softShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#64748b"/>
    </marker>
    
    <marker id="dataArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#8b5cf6"/>
    </marker>
  </defs>

  <!-- 背景 -->
  <rect width="1400" height="900" fill="#f8fafc"/>
  
  <!-- 标题 -->
  <text x="700" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="#1e293b">
    研究员智能体 MCP 工具集成架构
  </text>
  
  <!-- 主容器 -->
  <rect x="50" y="80" width="1300" height="780" rx="20" fill="white" stroke="#e2e8f0" stroke-width="2" filter="url(#softShadow)"/>
  
  <!-- 配置层 -->
  <rect x="100" y="120" width="1200" height="120" rx="15" fill="url(#configGradient)" opacity="0.1" stroke="url(#configGradient)" stroke-width="2"/>
  <text x="700" y="150" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#2563eb">
    配置层 (Configuration Layer)
  </text>
  
  <!-- 配置组件 -->
  <g transform="translate(150, 170)">
    <rect x="0" y="0" width="200" height="50" rx="8" fill="white" stroke="#3b82f6" stroke-width="1"/>
    <text x="100" y="20" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2563eb">mcp_settings</text>
    <text x="100" y="35" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">MCP服务器配置</text>
  </g>
  
  <g transform="translate(400, 170)">
    <rect x="0" y="0" width="200" height="50" rx="8" fill="white" stroke="#3b82f6" stroke-width="1"/>
    <text x="100" y="20" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2563eb">enabled_tools</text>
    <text x="100" y="35" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">启用的工具列表</text>
  </g>
  
  <g transform="translate(650, 170)">
    <rect x="0" y="0" width="200" height="50" rx="8" fill="white" stroke="#3b82f6" stroke-width="1"/>
    <text x="100" y="20" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2563eb">add_to_agents</text>
    <text x="100" y="35" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">Agent分配配置</text>
  </g>
  
  <g transform="translate(900, 170)">
    <rect x="0" y="0" width="200" height="50" rx="8" fill="white" stroke="#3b82f6" stroke-width="1"/>
    <text x="100" y="20" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#2563eb">transport/url/headers</text>
    <text x="100" y="35" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">连接配置</text>
  </g>

  <!-- 工具设置层 -->
  <rect x="100" y="280" width="1200" height="140" rx="15" fill="url(#mcpGradient)" opacity="0.1" stroke="url(#mcpGradient)" stroke-width="2"/>
  <text x="700" y="310" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#7c3aed">
    MCP工具设置层 (MCP Tools Setup)
  </text>
  
  <!-- setup_mcp_tools 流程 -->
  <g transform="translate(150, 330)">
    <rect x="0" y="0" width="180" height="60" rx="8" fill="white" stroke="#8b5cf6" stroke-width="2"/>
    <text x="90" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#7c3aed">setup_mcp_tools()</text>
    <text x="90" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">1. 收集服务器配置</text>
    <text x="90" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">2. 过滤agent工具</text>
  </g>
  
  <g transform="translate(370, 330)">
    <rect x="0" y="0" width="180" height="60" rx="8" fill="white" stroke="#8b5cf6" stroke-width="2"/>
    <text x="90" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#7c3aed">MultiServerMCPClient</text>
    <text x="90" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">创建MCP客户端</text>
    <text x="90" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">管理多服务器连接</text>
  </g>
  
  <g transform="translate(590, 330)">
    <rect x="0" y="0" width="180" height="60" rx="8" fill="white" stroke="#8b5cf6" stroke-width="2"/>
    <text x="90" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#7c3aed">get_tools()</text>
    <text x="90" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">获取所有工具</text>
    <text x="90" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">动态工具发现</text>
  </g>
  
  <g transform="translate(810, 330)">
    <rect x="0" y="0" width="180" height="60" rx="8" fill="white" stroke="#8b5cf6" stroke-width="2"/>
    <text x="90" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#7c3aed">filter_tools()</text>
    <text x="90" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">按enabled_tools过滤</text>
    <text x="90" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">添加服务器标识</text>
  </g>
  
  <g transform="translate(1030, 330)">
    <rect x="0" y="0" width="180" height="60" rx="8" fill="white" stroke="#8b5cf6" stroke-width="2"/>
    <text x="90" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#7c3aed">return filtered_tools</text>
    <text x="90" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">返回过滤后工具</text>
    <text x="90" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">供Agent使用</text>
  </g>

  <!-- 工具流程箭头 -->
  <path d="M 330 360 L 370 360" stroke="#8b5cf6" stroke-width="2" marker-end="url(#dataArrow)"/>
  <path d="M 550 360 L 590 360" stroke="#8b5cf6" stroke-width="2" marker-end="url(#dataArrow)"/>
  <path d="M 770 360 L 810 360" stroke="#8b5cf6" stroke-width="2" marker-end="url(#dataArrow)"/>
  <path d="M 990 360 L 1030 360" stroke="#8b5cf6" stroke-width="2" marker-end="url(#dataArrow)"/>

  <!-- Agent创建层 -->
  <rect x="100" y="460" width="1200" height="140" rx="15" fill="url(#llmGradient)" opacity="0.1" stroke="url(#llmGradient)" stroke-width="2"/>
  <text x="700" y="490" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#0891b2">
    Agent创建层 (Agent Creation)
  </text>
  
  <!-- create_agent_with_tools 流程 -->
  <g transform="translate(150, 510)">
    <rect x="0" y="0" width="180" height="60" rx="8" fill="white" stroke="#06b6d4" stroke-width="2"/>
    <text x="90" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#0891b2">get_llm_by_type()</text>
    <text x="90" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">获取LLM实例</text>
    <text x="90" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">AGENT_LLM_MAP</text>
  </g>
  
  <g transform="translate(370, 510)">
    <rect x="0" y="0" width="180" height="60" rx="8" fill="white" stroke="#06b6d4" stroke-width="2"/>
    <text x="90" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#0891b2">parallel_tool_calls</text>
    <text x="90" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">启用并发工具调用</text>
    <text x="90" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">提高执行效率</text>
  </g>
  
  <g transform="translate(590, 510)">
    <rect x="0" y="0" width="180" height="60" rx="8" fill="white" stroke="#06b6d4" stroke-width="2"/>
    <text x="90" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#0891b2">generate_prompt()</text>
    <text x="90" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">动态生成prompt</text>
    <text x="90" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">包含MCP工具描述</text>
  </g>
  
  <g transform="translate(810, 510)">
    <rect x="0" y="0" width="180" height="60" rx="8" fill="white" stroke="#06b6d4" stroke-width="2"/>
    <text x="90" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#0891b2">create_react_agent()</text>
    <text x="90" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">创建ReAct Agent</text>
    <text x="90" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">绑定LLM和工具</text>
  </g>
  
  <g transform="translate(1030, 510)">
    <rect x="0" y="0" width="180" height="60" rx="8" fill="white" stroke="#06b6d4" stroke-width="2"/>
    <text x="90" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#0891b2">return executor</text>
    <text x="90" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">返回执行器</text>
    <text x="90" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">可执行工具调用</text>
  </g>

  <!-- Agent创建流程箭头 -->
  <path d="M 330 540 L 370 540" stroke="#06b6d4" stroke-width="2" marker-end="url(#dataArrow)"/>
  <path d="M 550 540 L 590 540" stroke="#06b6d4" stroke-width="2" marker-end="url(#dataArrow)"/>
  <path d="M 770 540 L 810 540" stroke="#06b6d4" stroke-width="2" marker-end="url(#dataArrow)"/>
  <path d="M 990 540 L 1030 540" stroke="#06b6d4" stroke-width="2" marker-end="url(#dataArrow)"/>

  <!-- 工具描述生成层 -->
  <rect x="100" y="640" width="1200" height="140" rx="15" fill="url(#promptGradient)" opacity="0.1" stroke="url(#promptGradient)" stroke-width="2"/>
  <text x="700" y="670" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#d97706">
    工具描述生成层 (Tool Description Generation)
  </text>
  
  <!-- get_tools_description 流程 -->
  <g transform="translate(150, 690)">
    <rect x="0" y="0" width="180" height="60" rx="8" fill="white" stroke="#f59e0b" stroke-width="2"/>
    <text x="90" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#d97706">McpToolsService</text>
    <text x="90" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">fetch_mcp_tools()</text>
    <text x="90" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">获取所有工具</text>
  </g>
  
  <g transform="translate(370, 690)">
    <rect x="0" y="0" width="180" height="60" rx="8" fill="white" stroke="#f59e0b" stroke-width="2"/>
    <text x="90" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#d97706">filter by enabled_tools</text>
    <text x="90" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">按配置过滤工具</text>
    <text x="90" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">确保工具可用</text>
  </g>
  
  <g transform="translate(590, 690)">
    <rect x="0" y="0" width="180" height="60" rx="8" fill="white" stroke="#f59e0b" stroke-width="2"/>
    <text x="90" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#d97706">convert_to_openai_tool</text>
    <text x="90" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">转换工具格式</text>
    <text x="90" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">生成JSON Schema</text>
  </g>
  
  <g transform="translate(810, 690)">
    <rect x="0" y="0" width="180" height="60" rx="8" fill="white" stroke="#f59e0b" stroke-width="2"/>
    <text x="90" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#d97706">format description</text>
    <text x="90" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">格式化工具描述</text>
    <text x="90" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">包含输入Schema</text>
  </g>
  
  <g transform="translate(1030, 690)">
    <rect x="0" y="0" width="180" height="60" rx="8" fill="white" stroke="#f59e0b" stroke-width="2"/>
    <text x="90" y="25" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#d97706">## Available Tools</text>
    <text x="90" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">生成工具文档</text>
    <text x="90" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">供prompt使用</text>
  </g>

  <!-- 工具描述流程箭头 -->
  <path d="M 330 720 L 370 720" stroke="#f59e0b" stroke-width="2" marker-end="url(#dataArrow)"/>
  <path d="M 550 720 L 590 720" stroke="#f59e0b" stroke-width="2" marker-end="url(#dataArrow)"/>
  <path d="M 770 720 L 810 720" stroke="#f59e0b" stroke-width="2" marker-end="url(#dataArrow)"/>
  <path d="M 990 720 L 1030 720" stroke="#f59e0b" stroke-width="2" marker-end="url(#dataArrow)"/>

  <!-- 层间连接箭头 -->
  <path d="M 700 240 L 700 280" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 700 420 L 700 460" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 700 600 L 700 640" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>

  <!-- 关键特性标注 -->
  <rect x="80" y="800" width="300" height="80" rx="10" fill="#f0f9ff" stroke="#0ea5e9" stroke-width="1"/>
  <text x="230" y="825" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#0c4a6e">
    关键特性
  </text>
  <text x="90" y="845" font-family="Arial, sans-serif" font-size="12" fill="#0c4a6e">
    • 动态工具发现和过滤
  </text>
  <text x="90" y="860" font-family="Arial, sans-serif" font-size="12" fill="#0c4a6e">
    • 支持多种传输协议 (stdio/http)
  </text>
  <text x="90" y="875" font-family="Arial, sans-serif" font-size="12" fill="#0c4a6e">
    • 并发工具调用优化
  </text>
  
  <rect x="420" y="800" width="300" height="80" rx="10" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
  <text x="570" y="825" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#15803d">
    配置灵活性
  </text>
  <text x="430" y="845" font-family="Arial, sans-serif" font-size="12" fill="#15803d">
    • 按Agent分配工具
  </text>
  <text x="430" y="860" font-family="Arial, sans-serif" font-size="12" fill="#15803d">
    • 启用/禁用特定工具
  </text>
  <text x="430" y="875" font-family="Arial, sans-serif" font-size="12" fill="#15803d">
    • 多服务器支持
  </text>
  
  <rect x="760" y="800" width="300" height="80" rx="10" fill="#fefce8" stroke="#eab308" stroke-width="1"/>
  <text x="910" y="825" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#a16207">
    智能集成
  </text>
  <text x="770" y="845" font-family="Arial, sans-serif" font-size="12" fill="#a16207">
    • 自动生成工具文档
  </text>
  <text x="770" y="860" font-family="Arial, sans-serif" font-size="12" fill="#a16207">
    • 动态prompt构建
  </text>
  <text x="770" y="875" font-family="Arial, sans-serif" font-size="12" fill="#a16207">
    • 错误处理和恢复
  </text>
  
  <rect x="1100" y="800" width="200" height="80" rx="10" fill="#fdf2f8" stroke="#ec4899" stroke-width="1"/>
  <text x="1200" y="825" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#be185d">
    性能优化
  </text>
  <text x="1110" y="845" font-family="Arial, sans-serif" font-size="12" fill="#be185d">
    • 工具缓存机制
  </text>
  <text x="1110" y="860" font-family="Arial, sans-serif" font-size="12" fill="#be185d">
    • 连接池管理
  </text>
  <text x="1110" y="875" font-family="Arial, sans-serif" font-size="12" fill="#be185d">
    • 递归限制控制
  </text>
</svg>