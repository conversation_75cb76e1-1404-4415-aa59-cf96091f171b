<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" viewBox="0 0 1400 1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 现代渐变色彩定义 -->
    <linearGradient id="baseAgentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="researcherGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="stepExecutorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="contextBuilderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="utilsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0891b2;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="highlightGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b91c1c;stop-opacity:1" />
    </linearGradient>
    
    <!-- 柔和阴影滤镜 -->
    <filter id="softShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
    
    <!-- 强调阴影滤镜 -->
    <filter id="highlightShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="#dc2626" flood-opacity="0.3"/>
    </filter>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#64748b"/>
    </marker>
    
    <marker id="compositionArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#8b5cf6"/>
    </marker>
  </defs>

  <!-- 背景 -->
  <rect width="1400" height="1000" fill="#f8fafc"/>
  
  <!-- 标题 -->
  <text x="700" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="#1e293b">
    研究员智能体详细架构
  </text>
  
  <!-- 关键设计优点区域 -->
  <rect x="50" y="60" width="1300" height="80" rx="15" fill="#ecfdf5" stroke="#22c55e" stroke-width="2"/>
  <text x="700" y="85" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#15803d">
    关键设计优点
  </text>
  <text x="100" y="105" font-family="Arial, sans-serif" font-size="12" fill="#15803d">
    ✓ 单一职责原则：每个组件专注特定功能
  </text>
  <text x="450" y="105" font-family="Arial, sans-serif" font-size="12" fill="#15803d">
    ✓ 组合优于继承：灵活的组件组合
  </text>
  <text x="800" y="105" font-family="Arial, sans-serif" font-size="12" fill="#15803d">
    ✓ 依赖注入：松耦合的组件关系
  </text>
  <text x="100" y="125" font-family="Arial, sans-serif" font-size="12" fill="#15803d">
    ✓ 策略模式：可扩展的上下文构建
  </text>
  <text x="450" y="125" font-family="Arial, sans-serif" font-size="12" fill="#15803d">
    ✓ 模板方法：统一的执行流程
  </text>
  <text x="800" y="125" font-family="Arial, sans-serif" font-size="12" fill="#15803d">
    ✓ 错误恢复：完善的异常处理机制
  </text>
  
  <!-- 主容器 -->
  <rect x="50" y="160" width="1300" height="800" rx="20" fill="white" stroke="#e2e8f0" stroke-width="2" filter="url(#softShadow)"/>
  
  <!-- BaseAgent层 -->
  <rect x="100" y="200" width="1200" height="100" rx="15" fill="url(#baseAgentGradient)" opacity="0.1" stroke="url(#baseAgentGradient)" stroke-width="2"/>
  <text x="700" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#1d4ed8">
    BaseAgent (抽象基类)
  </text>
  <text x="700" y="245" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#64748b">
    提供统一的执行模板和错误处理机制
  </text>
  
  <!-- BaseAgent 核心方法 -->
  <g transform="translate(200, 260)">
    <rect x="0" y="0" width="160" height="30" rx="6" fill="white" stroke="#3b82f6" stroke-width="1"/>
    <text x="80" y="20" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#1d4ed8">execute() - 统一入口</text>
  </g>
  
  <g transform="translate(400, 260)">
    <rect x="0" y="0" width="160" height="30" rx="6" fill="white" stroke="#3b82f6" stroke-width="1"/>
    <text x="80" y="20" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#1d4ed8">_do_execute() - 抽象</text>
  </g>
  
  <g transform="translate(600, 260)">
    <rect x="0" y="0" width="160" height="30" rx="6" fill="white" stroke="#3b82f6" stroke-width="1"/>
    <text x="80" y="20" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#1d4ed8">_handle_error() - 恢复</text>
  </g>
  
  <g transform="translate(800, 260)">
    <rect x="0" y="0" width="160" height="30" rx="6" fill="white" stroke="#3b82f6" stroke-width="1"/>
    <text x="80" y="20" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#1d4ed8">get_default_tools()</text>
  </g>

  <!-- 继承箭头 -->
  <path d="M 700 300 L 700 330" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- ResearcherAgent 主体 -->
  <rect x="100" y="350" width="1200" height="120" rx="15" fill="url(#researcherGradient)" opacity="0.1" stroke="url(#researcherGradient)" stroke-width="2"/>
  <text x="700" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#059669">
    ResearcherAgent - 系统诊断的核心执行者
  </text>
  <text x="700" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#64748b">
    专注于ECS故障诊断，通过组合StepExecutor实现复杂的诊断流程
  </text>
  
  <!-- ResearcherAgent 核心属性 -->
  <g transform="translate(300, 410)">
    <rect x="0" y="0" width="200" height="40" rx="8" fill="white" stroke="#10b981" stroke-width="1"/>
    <text x="100" y="18" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#059669">step_executor</text>
    <text x="100" y="32" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#64748b">组合的步骤执行器</text>
  </g>

  <!-- 核心方法 - 高亮显示 -->
  <g transform="translate(550, 405)">
    <rect x="0" y="0" width="300" height="50" rx="10" fill="url(#highlightGradient)" stroke="#dc2626" stroke-width="3" filter="url(#highlightShadow)"/>
    <text x="150" y="20" text-anchor="middle" font-family="Arial, sans-serif" font-size="13" font-weight="bold" fill="white">🔥 _do_execute() - 核心方法</text>
    <text x="150" y="35" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">验证条件 → 委托执行 → 错误处理</text>
    <text x="150" y="48" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">实现模板方法模式，确保执行流程一致性</text>
  </g>

  <!-- 组合关系箭头 -->
  <path d="M 700 470 L 700 510" stroke="#8b5cf6" stroke-width="3" marker-end="url(#compositionArrow)"/>
  <text x="720" y="490" font-family="Arial, sans-serif" font-size="12" fill="#8b5cf6" font-weight="bold">组合</text>
  
  <!-- StepExecutor 组件 -->
  <rect x="100" y="530" width="580" height="180" rx="15" fill="url(#stepExecutorGradient)" opacity="0.1" stroke="url(#stepExecutorGradient)" stroke-width="2"/>
  <text x="390" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#7c3aed">
    StepExecutor - 步骤执行引擎
  </text>
  <text x="390" y="575" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#64748b">
    负责具体步骤的执行逻辑，集成MCP工具和上下文构建
  </text>
  
  <!-- 方法价值说明 -->
  <rect x="120" y="590" width="540" height="30" rx="5" fill="#fef3c7" stroke="#f59e0b" stroke-width="1"/>
  <text x="390" y="610" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#92400e">
    方法特点：工具集成 + 状态管理 + 并发执行 + 错误恢复
  </text>
  
  <!-- StepExecutor 核心方法 -->
  <g transform="translate(120, 630)">
    <rect x="0" y="0" width="130" height="60" rx="8" fill="url(#highlightGradient)" stroke="#dc2626" stroke-width="2" filter="url(#highlightShadow)"/>
    <text x="65" y="18" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">🔥 execute_current_step()</text>
    <text x="65" y="32" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">核心执行逻辑</text>
    <text x="65" y="44" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">工具准备→执行→更新</text>
    <text x="65" y="56" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">支持并发工具调用</text>
  </g>
  
  <g transform="translate(270, 630)">
    <rect x="0" y="0" width="120" height="60" rx="8" fill="white" stroke="#8b5cf6" stroke-width="1"/>
    <text x="60" y="18" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#7c3aed">_find_current_step()</text>
    <text x="60" y="32" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#64748b">智能步骤发现</text>
    <text x="60" y="44" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#64748b">状态感知查找</text>
  </g>
  
  <g transform="translate(410, 630)">
    <rect x="0" y="0" width="120" height="60" rx="8" fill="white" stroke="#8b5cf6" stroke-width="1"/>
    <text x="60" y="18" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#7c3aed">_prepare_tools()</text>
    <text x="60" y="32" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#64748b">动态工具配置</text>
    <text x="60" y="44" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#64748b">MCP集成</text>
  </g>
  
  <g transform="translate(550, 630)">
    <rect x="0" y="0" width="120" height="60" rx="8" fill="white" stroke="#8b5cf6" stroke-width="1"/>
    <text x="60" y="18" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#7c3aed">_prepare_input()</text>
    <text x="60" y="32" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#64748b">输入预处理</text>
    <text x="60" y="44" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#64748b">上下文委托</text>
  </g>

  <!-- StepContextBuilder 组件 -->
  <rect x="720" y="530" width="580" height="180" rx="15" fill="url(#contextBuilderGradient)" opacity="0.1" stroke="url(#contextBuilderGradient)" stroke-width="2"/>
  <text x="1010" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#d97706">
    StepContextBuilder - 智能上下文构建器
  </text>
  <text x="1010" y="575" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#64748b">
    构建完整的执行上下文，确保Agent获得充分的历史信息和任务描述
  </text>
  
  <!-- 方法价值说明 -->
  <rect x="740" y="590" width="540" height="30" rx="5" fill="#fef3c7" stroke="#f59e0b" stroke-width="1"/>
  <text x="1010" y="610" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="#92400e">
    方法特点：历史感知 + 任务描述 + 指令定制 + 结构化输出
  </text>
  
  <!-- StepContextBuilder 核心方法 -->
  <g transform="translate(740, 630)">
    <rect x="0" y="0" width="130" height="60" rx="8" fill="url(#highlightGradient)" stroke="#dc2626" stroke-width="2" filter="url(#highlightShadow)"/>
    <text x="65" y="18" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">🔥 build_input()</text>
    <text x="65" y="32" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">核心构建逻辑</text>
    <text x="65" y="44" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">历史→任务→指令</text>
    <text x="65" y="56" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">结构化消息组装</text>
  </g>
  
  <g transform="translate(890, 630)">
    <rect x="0" y="0" width="120" height="60" rx="8" fill="white" stroke="#f59e0b" stroke-width="1"/>
    <text x="60" y="18" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#d97706">_build_history_context()</text>
    <text x="60" y="32" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#64748b">历史步骤整合</text>
    <text x="60" y="44" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#64748b">上下文连续性</text>
  </g>
  
  <g transform="translate(1030, 630)">
    <rect x="0" y="0" width="120" height="60" rx="8" fill="white" stroke="#f59e0b" stroke-width="1"/>
    <text x="60" y="18" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#d97706">_build_current_task()</text>
    <text x="60" y="32" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#64748b">任务描述生成</text>
    <text x="60" y="44" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#64748b">清晰的执行目标</text>
  </g>
  
  <g transform="translate(1170, 630)">
    <rect x="0" y="0" width="120" height="60" rx="8" fill="white" stroke="#f59e0b" stroke-width="1"/>
    <text x="60" y="18" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#d97706">_build_agent_instruction()</text>
    <text x="60" y="32" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#64748b">定制化指令</text>
    <text x="60" y="44" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#64748b">Agent特定规则</text>
  </g>

  <!-- 使用关系箭头 -->
  <path d="M 630 650 L 740 650" stroke="#64748b" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
  <text x="685" y="645" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#64748b">使用</text>

  <!-- Utils 工具函数层 -->
  <rect x="100" y="750" width="1200" height="80" rx="15" fill="url(#utilsGradient)" opacity="0.1" stroke="url(#utilsGradient)" stroke-width="2"/>
  <text x="700" y="775" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#0891b2">
    Utils 工具函数层 - 基础设施支撑
  </text>
  <text x="700" y="795" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#64748b">
    提供MCP集成、Agent创建、配置管理等基础功能
  </text>
  
  <g transform="translate(150, 805)">
    <rect x="0" y="0" width="150" height="20" rx="4" fill="white" stroke="#06b6d4" stroke-width="1"/>
    <text x="75" y="15" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#0891b2">setup_mcp_tools()</text>
  </g>
  
  <g transform="translate(320, 805)">
    <rect x="0" y="0" width="150" height="20" rx="4" fill="white" stroke="#06b6d4" stroke-width="1"/>
    <text x="75" y="15" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#0891b2">create_agent_with_tools()</text>
  </g>
  
  <g transform="translate(490, 805)">
    <rect x="0" y="0" width="150" height="20" rx="4" fill="white" stroke="#06b6d4" stroke-width="1"/>
    <text x="75" y="15" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#0891b2">get_recursion_limit()</text>
  </g>
  
  <g transform="translate(660, 805)">
    <rect x="0" y="0" width="150" height="20" rx="4" fill="white" stroke="#06b6d4" stroke-width="1"/>
    <text x="75" y="15" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#0891b2">apply_prompt_template()</text>
  </g>
  
  <g transform="translate(830, 805)">
    <rect x="0" y="0" width="150" height="20" rx="4" fill="white" stroke="#06b6d4" stroke-width="1"/>
    <text x="75" y="15" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#0891b2">get_tools_description()</text>
  </g>

  <!-- 依赖关系箭头 -->
  <path d="M 390 710 L 500 750" stroke="#64748b" stroke-width="1" stroke-dasharray="3,3" marker-end="url(#arrowhead)"/>
  <path d="M 1010 710 L 900 750" stroke="#64748b" stroke-width="1" stroke-dasharray="3,3" marker-end="url(#arrowhead)"/>
  
  <!-- 核心方法详细描述区域 -->
  <rect x="100" y="860" width="1200" height="80" rx="10" fill="#fef2f2" stroke="#dc2626" stroke-width="2"/>
  <text x="700" y="885" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#dc2626">
    🔥 核心方法详细描述
  </text>
  <text x="120" y="905" font-family="Arial, sans-serif" font-size="11" fill="#dc2626">
    <tspan font-weight="bold">_do_execute():</tspan> 实现模板方法模式，先验证执行条件，再委托给StepExecutor，最后处理错误。确保执行流程的一致性和可靠性。
  </text>
  <text x="120" y="920" font-family="Arial, sans-serif" font-size="11" fill="#dc2626">
    <tspan font-weight="bold">execute_current_step():</tspan> 步骤执行的核心引擎，集成工具准备、上下文构建、Agent创建和结果处理。支持并发工具调用，提高诊断效率。
  </text>
  <text x="120" y="935" font-family="Arial, sans-serif" font-size="11" fill="#dc2626">
    <tspan font-weight="bold">build_input():</tspan> 智能上下文构建器，整合历史步骤、当前任务和Agent指令，为LLM提供完整的执行上下文，确保诊断的连续性和准确性。
  </text>
</svg>